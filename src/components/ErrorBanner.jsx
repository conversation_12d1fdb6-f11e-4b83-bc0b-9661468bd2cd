import React from 'react';

const ErrorBanner = ({ 
  chatError, 
  dbError, 
  resetChatError, 
  resetDbError, 
  isOnline 
}) => {
  if (!chatError && !dbError && isOnline) return null;

  return (
    <div className="error-banner">
      {!isOnline && (
        <div className="error-message offline">
          <span>⚠️ Connection lost. Check your internet connection.</span>
        </div>
      )}
      
      {chatError && (
        <div className="error-message chat-error">
          <span>Chat Error: {chatError}</span>
          <button onClick={resetChatError} className="error-dismiss">
            ✕
          </button>
        </div>
      )}
      
      {dbError && (
        <div className="error-message db-error">
          <span>Database Error: {dbError}</span>
          <button onClick={resetDbError} className="error-dismiss">
            ✕
          </button>
        </div>
      )}
    </div>
  );
};

export default ErrorBanner;
