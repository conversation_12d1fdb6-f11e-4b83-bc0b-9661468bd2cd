import { useState } from 'react';

const DatabaseSidebar = ({
  dbLoading,
  dbTables,
  selectedTable,
  setSelectedTable
}) => {
  const [expandedTables, setExpandedTables] = useState(new Set());
  const [searchTerm, setSearchTerm] = useState('');

  const toggleTable = (tableName) => {
    const newExpanded = new Set(expandedTables);
    if (newExpanded.has(tableName)) {
      newExpanded.delete(tableName);
    } else {
      newExpanded.add(tableName);
    }
    setExpandedTables(newExpanded);
  };

  const handleTableSelect = (table) => {
    setSelectedTable(selectedTable?.name === table.name ? null : table);
  };

  const handleTableNameClick = (table) => {
    // Toggle expansion when clicking on table name
    toggleTable(table.name);
    // Also select the table
    handleTableSelect(table);
  };

  // Filter tables based on search term
  const filteredTables = dbTables.filter(table =>
    table.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="database-sidebar">
      <div className="sidebar-header">
        <h2>Database Tables</h2>
        {dbLoading && <div className="loading-spinner">⟳</div>}
      </div>

      <div className="search-container">
        <div className="search-wrapper">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="search-icon">
            <circle cx="11" cy="11" r="8"/>
            <path d="M21 21l-4.35-4.35"/>
          </svg>
          <input
            type="text"
            placeholder="Search database tables..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="clear-search"
              title="Clear search"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M18 6L6 18M6 6l12 12"/>
              </svg>
            </button>
          )}
        </div>
      </div>

      <div className="tables-list">
        {dbLoading ? (
          <div className="loading">
            <div className="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            Loading tables...
          </div>
        ) : filteredTables.length === 0 ? (
          <div className="no-tables">
            {searchTerm ? `No tables found matching "${searchTerm}"` : 'No tables found'}
          </div>
        ) : (
          filteredTables.map((table) => (
            <div key={table.name} className="table-item">
              <div className="table-row">
                <div className="table-info" onClick={() => handleTableNameClick(table)}>
                  <div className="table-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                      <rect x="3" y="3" width="18" height="18" rx="2" ry="2" fill="rgba(168, 85, 247, 0.1)"/>
                      <path d="M3 9h18M3 15h18" stroke="rgba(168, 85, 247, 0.6)"/>
                      <path d="M9 3v18M15 3v18" stroke="rgba(168, 85, 247, 0.6)"/>
                      <rect x="3" y="3" width="18" height="6" fill="rgba(168, 85, 247, 0.2)"/>
                    </svg>
                  </div>
                  <span className="table-name">{table.name}</span>
                </div>
                <button
                  className="expand-btn"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleTable(table.name);
                  }}
                  title={expandedTables.has(table.name) ? 'Hide columns' : 'Show columns'}
                >
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"
                       style={{ transform: expandedTables.has(table.name) ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'transform 0.2s ease' }}>
                    <path d="M6 9l6 6 6-6"/>
                  </svg>
                </button>
              </div>

              {expandedTables.has(table.name) && (
                <div className="table-columns">
                  {table.columns?.map((column, index) => (
                    <div key={index} className="column-item">
                      <span className="column-name">{column.name}</span>
                    </div>
                  )) || (
                    <div className="no-columns">No column information available</div>
                  )}
                </div>
              )}
            </div>
          ))
        )}
      </div>


    </div>
  );
};

export default DatabaseSidebar;
