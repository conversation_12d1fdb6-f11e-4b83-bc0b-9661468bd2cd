import { useState } from 'react';

const ChatSidebar = ({
  chatLoading,
  chats,
  currentChat,
  setCurrentChat,
  loadMessages,
  createNewChat,
  updateChatTitle,
  deleteChat,
  archiveChat,
  unarchiveChat
}) => {
  const [editingChat, setEditingChat] = useState(null);
  const [editTitle, setEditTitle] = useState('');

  const handleChatSelect = async (chat) => {
    setCurrentChat(chat);
    try {
      await loadMessages(chat.id);
    } catch (error) {
      console.error('Failed to load messages:', error);
    }
  };

  const handleEditStart = (chat) => {
    setEditingChat(chat.id);
    setEditTitle(chat.title);
  };

  const handleEditSave = async () => {
    if (editingChat && editTitle.trim()) {
      try {
        await updateChatTitle(editingChat, editTitle.trim());
        setEditingChat(null);
      } catch (error) {
        console.error('Failed to update chat title:', error);
      }
    }
  };

  const handleEditCancel = () => {
    setEditingChat(null);
    setEditTitle('');
  };

  const handleDelete = async (chatId) => {
    if (window.confirm('Are you sure you want to delete this chat?')) {
      try {
        await deleteChat(chatId);
      } catch (error) {
        console.error('Failed to delete chat:', error);
      }
    }
  };

  const activeChats = chats.filter(chat => !chat.archived);
  const archivedChats = chats.filter(chat => chat.archived);

  return (
    <div className="sidebar">
      <div className="new-chat-button-container">
        <button
          onClick={createNewChat}
          className="new-chat-button ripple"
          disabled={chatLoading}
          title="Start new chat"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
            <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
          </svg>
          <span>New Chat</span>
        </button>
      </div>

      <div className="sidebar-header">
        <h2>Chats</h2>
        {chatLoading && <div className="loading-spinner">⟳</div>}
      </div>

      <div className="chat-list">
        {chatLoading ? (
          <div className="loading">
            <div className="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            Loading chats...
          </div>
        ) : (
          <>
            {activeChats.map(chat => (
              <div
                key={chat.id}
                className={`chat-item ${currentChat?.id === chat.id ? 'active' : ''}`}
                onClick={() => handleChatSelect(chat)}
              >
                {editingChat === chat.id ? (
                  <div className="edit-chat">
                    <input
                      type="text"
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleEditSave()}
                      onBlur={handleEditSave}
                      autoFocus
                    />
                  </div>
                ) : (
                  <>
                    <span className="chat-title">{chat.title}</span>
                    <div className="chat-actions">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditStart(chat);
                        }}
                        className="edit-btn"
                        title="Edit chat title"
                      >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                          <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                        </svg>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          archiveChat(chat.id);
                        }}
                        className="archive-btn"
                        title="Archive chat"
                      >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                          <path d="M9 9h6v6H9z"/>
                        </svg>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(chat.id);
                        }}
                        className="delete-btn"
                        title="Delete chat"
                      >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z"/>
                          <path d="M10 11v6M14 11v6"/>
                        </svg>
                      </button>
                    </div>
                  </>
                )}
              </div>
            ))}

            {archivedChats.length > 0 && (
              <div className="archived-section">
                <h3>Archived</h3>
                {archivedChats.map(chat => (
                  <div
                    key={chat.id}
                    className="chat-item archived"
                    onClick={() => handleChatSelect(chat)}
                  >
                    <span className="chat-title">{chat.title}</span>
                    <div className="chat-actions">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          unarchiveChat(chat.id);
                        }}
                        className="unarchive-btn"
                        title="Unarchive chat"
                      >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M21 8.5l-10 5.5-10-5.5"/>
                          <path d="M21 16.5l-10 5.5-10-5.5"/>
                          <path d="M21 12.5l-10 5.5-10-5.5"/>
                        </svg>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(chat.id);
                        }}
                        className="delete-btn"
                        title="Delete chat"
                      >
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z"/>
                          <path d="M10 11v6M14 11v6"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ChatSidebar;
