/**
 * AI-Powered Message Classification for Frontend
 * Replaces hardcoded conversational phrase detection with intelligent classification
 */

class AIMessageClassifier {
  constructor() {
    this.apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
  }

  /**
   * Classify message intent using backend AI instead of hardcoded patterns
   * @param {string} message - User's message
   * @param {Object} context - Additional context
   * @returns {Promise<Object>} Classification result
   */
  async classifyMessage(message, context = {}) {
    try {
      // Call backend AI classification endpoint
      const response = await fetch(`${this.apiBaseUrl}/ai/classify-intent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: message,
          context: {
            timestamp: new Date().toISOString(),
            source: 'frontend',
            ...context
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Classification failed: ${response.status}`);
      }

      const result = await response.json();
      return {
        success: true,
        intent: result.intent,
        confidence: result.confidence,
        isConversational: !result.requires_sql,
        isDatabaseQuery: result.requires_sql,
        reasoning: result.reasoning,
        suggestedResponseType: result.suggested_response_type
      };

    } catch (error) {
      console.warn('AI classification failed, using fallback:', error);
      return this.fallbackClassification(message);
    }
  }

  /**
   * Fallback classification if AI service is unavailable
   * @param {string} message - User's message
   * @returns {Object} Basic classification
   */
  fallbackClassification(message) {
    const lowerMessage = message.toLowerCase().trim();
    
    // Simple heuristic fallback
    const greetingWords = ['hi', 'hello', 'hey', 'good morning', 'good afternoon'];
    const queryWords = ['show', 'list', 'get', 'count', 'how many', 'select'];
    const helpWords = ['help', 'what can you do', 'how do you work'];

    let intent = 'GENERAL_CONVERSATION';
    let isConversational = true;
    let confidence = 0.5;

    if (greetingWords.some(word => lowerMessage.includes(word))) {
      intent = 'GREETING';
      confidence = 0.7;
    } else if (queryWords.some(word => lowerMessage.includes(word))) {
      intent = 'DATABASE_QUERY';
      isConversational = false;
      confidence = 0.6;
    } else if (helpWords.some(word => lowerMessage.includes(word))) {
      intent = 'HELP_REQUEST';
      confidence = 0.6;
    }

    return {
      success: false,
      intent,
      confidence,
      isConversational,
      isDatabaseQuery: !isConversational,
      reasoning: 'Fallback classification used',
      suggestedResponseType: isConversational ? 'conversational' : 'sql_generation'
    };
  }

  /**
   * Determine if query modal should be shown based on AI classification
   * @param {string} message - User's message
   * @param {Object} response - Backend response
   * @returns {Promise<boolean>} Whether to show query modal
   */
  async shouldShowQueryModal(message, response) {
    try {
      const classification = await this.classifyMessage(message);
      
      // Show modal only for database queries that have SQL results
      return (
        classification.isDatabaseQuery &&
        classification.confidence > 0.5 &&
        (response.query_result || response.sql_query)
      );
    } catch (error) {
      console.warn('Error determining modal visibility:', error);
      // Fallback: show modal if response has query data
      return !!(response.query_result || response.sql_query);
    }
  }

  /**
   * Get dynamic placeholder text based on context
   * @param {Object} context - Current context
   * @returns {Promise<string>} Dynamic placeholder text
   */
  async getDynamicPlaceholder(context = {}) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/ai/generate-placeholder`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          context: {
            hasActiveChat: !!context.currentChat,
            timeOfDay: this.getTimeOfDay(),
            ...context
          }
        })
      });

      if (response.ok) {
        const result = await response.json();
        return result.placeholder;
      }
    } catch (error) {
      console.warn('Failed to get dynamic placeholder:', error);
    }

    // Fallback placeholders
    const fallbackPlaceholders = [
      "Ask me about your data...",
      "What would you like to know?",
      "Type your question here...",
      "How can I help with your database?",
      "Ask me anything about your data..."
    ];

    return fallbackPlaceholders[Math.floor(Math.random() * fallbackPlaceholders.length)];
  }

  /**
   * Get time of day for context
   * @returns {string} Time period
   */
  getTimeOfDay() {
    const hour = new Date().getHours();
    if (hour < 12) return 'morning';
    if (hour < 17) return 'afternoon';
    if (hour < 21) return 'evening';
    return 'night';
  }

  /**
   * Generate dynamic empty state messages
   * @param {Object} context - Current context
   * @returns {Promise<Object>} Dynamic empty state content
   */
  async getDynamicEmptyState(context = {}) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/ai/generate-empty-state`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ context })
      });

      if (response.ok) {
        const result = await response.json();
        return {
          title: result.title,
          description: result.description,
          suggestions: result.suggestions || []
        };
      }
    } catch (error) {
      console.warn('Failed to get dynamic empty state:', error);
    }

    // Fallback empty state
    return {
      title: "Start a conversation",
      description: "Send a message to begin your chat with the AI assistant.",
      suggestions: [
        "Show me all users",
        "How many orders do we have?",
        "List recent customers"
      ]
    };
  }
}

// Export singleton instance
export const aiClassifier = new AIMessageClassifier();
export default aiClassifier;
