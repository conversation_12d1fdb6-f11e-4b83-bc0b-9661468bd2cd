/**
 * Error tracking and performance monitoring utilities
 */

import * as Sentry from '@sentry/react';
import { onCLS, onINP, onFCP, onLCP, onTTFB } from 'web-vitals';

// Initialize Sentry error tracking
export const initErrorTracking = () => {
  const sentryDsn = import.meta.env.VITE_SENTRY_DSN;
  const environment = import.meta.env.VITE_SENTRY_ENVIRONMENT || 'development';
  const appVersion = import.meta.env.VITE_APP_VERSION || '1.0.0';

  if (sentryDsn && import.meta.env.PROD) {
    Sentry.init({
      dsn: sentryDsn,
      environment,
      release: appVersion,
      integrations: [
        // Add integrations as needed
      ],
      // Performance Monitoring
      tracesSampleRate: import.meta.env.PROD ? 0.1 : 1.0,
      // Session Replay
      replaysSessionSampleRate: 0.1,
      replaysOnErrorSampleRate: 1.0,
      // Error filtering
      beforeSend(event, hint) {
        // Filter out development errors
        if (import.meta.env.DEV) {
          return null;
        }

        // Filter out network errors that are not actionable
        if (event.exception) {
          const error = hint.originalException;
          if (error instanceof Error) {
            if (error.message.includes('Network Error') ||
                error.message.includes('Failed to fetch')) {
              return null;
            }
          }
        }

        return event;
      },
    });
  }
};

// Error logging utility
export const logError = (error, context) => {
  console.error('Error:', error, context);

  if (import.meta.env.PROD) {
    Sentry.withScope((scope) => {
      if (context) {
        Object.keys(context).forEach(key => {
          scope.setContext(key, context[key]);
        });
      }
      Sentry.captureException(error);
    });
  }
};

// Performance monitoring
export const initPerformanceMonitoring = () => {
  if (!import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING) {
    return;
  }

  const sendToAnalytics = (metric) => {
    // Send to your analytics service
    console.log('Performance metric:', metric);

    if (import.meta.env.PROD) {
      // Example: Send to Google Analytics
      // gtag('event', metric.name, {
      //   value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      //   event_label: metric.id,
      //   non_interaction: true,
      // });

      // Send to Sentry as performance data
      Sentry.addBreadcrumb({
        category: 'performance',
        message: `${metric.name}: ${metric.value}`,
        level: 'info',
        data: {
          name: metric.name,
          value: metric.value,
          id: metric.id,
          delta: metric.delta,
        },
      });
    }
  };

  // Core Web Vitals
  onCLS(sendToAnalytics);
  onINP(sendToAnalytics); // INP replaced FID in web-vitals v3
  onFCP(sendToAnalytics);
  onLCP(sendToAnalytics);
  onTTFB(sendToAnalytics);
};

// User interaction tracking
export const trackUserAction = (action, data) => {
  console.log('User action:', action, data);

  if (import.meta.env.PROD && import.meta.env.VITE_ENABLE_ANALYTICS) {
    // Send to analytics service
    Sentry.addBreadcrumb({
      category: 'user',
      message: action,
      level: 'info',
      data,
    });
  }
};

// API call tracking
export const trackApiCall = (endpoint, method, duration, status) => {
  const data = {
    endpoint,
    method,
    duration,
    status,
  };

  console.log('API call:', data);

  if (import.meta.env.PROD) {
    Sentry.addBreadcrumb({
      category: 'http',
      message: `${method} ${endpoint}`,
      level: status >= 400 ? 'error' : 'info',
      data,
    });
  }
};

// Feature flag tracking
export const trackFeatureUsage = (feature, enabled) => {
  if (import.meta.env.PROD && import.meta.env.VITE_ENABLE_ANALYTICS) {
    Sentry.setTag(`feature.${feature}`, enabled);
  }
};

// Custom performance marks
export const markPerformance = (name) => {
  if (performance.mark) {
    performance.mark(name);
  }
};

export const measurePerformance = (name, startMark, endMark) => {
  if (performance.measure) {
    try {
      performance.measure(name, startMark, endMark);
      const measure = performance.getEntriesByName(name, 'measure')[0];

      if (measure && import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING) {
        console.log(`Performance: ${name} took ${measure.duration}ms`);

        if (import.meta.env.PROD) {
          Sentry.addBreadcrumb({
            category: 'performance',
            message: `${name}: ${measure.duration}ms`,
            level: 'info',
            data: {
              name,
              duration: measure.duration,
              startTime: measure.startTime,
            },
          });
        }
      }
    } catch (error) {
      console.warn('Performance measurement failed:', error);
    }
  }
};

// Memory usage monitoring
export const monitorMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = performance.memory;
    const memoryInfo = {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
    };

    console.log('Memory usage:', memoryInfo);

    if (import.meta.env.PROD && import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING) {
      Sentry.setContext('memory', memoryInfo);
    }
  }
};

// Initialize all monitoring
export const initMonitoring = () => {
  initErrorTracking();
  initPerformanceMonitoring();

  // Monitor memory usage periodically
  if (import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING) {
    setInterval(monitorMemoryUsage, 30000); // Every 30 seconds
  }
};
