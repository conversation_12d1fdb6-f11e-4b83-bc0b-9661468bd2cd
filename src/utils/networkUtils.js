/**
 * Network utility functions for enhanced error handling and connectivity detection
 */

// Network status detection
export const getNetworkStatus = () => {
  return {
    online: navigator.onLine,
    connection: navigator.connection || navigator.mozConnection || navigator.webkitConnection,
    effectiveType: navigator.connection?.effectiveType || 'unknown'
  };
};

// Check if error is network-related
export const isNetworkError = (error) => {
  if (!error) return false;

  // Check for common network error patterns
  const networkErrorPatterns = [
    /network error/i,
    /failed to fetch/i,
    /connection refused/i,
    /timeout/i,
    /econnaborted/i,
    /enotfound/i,
    /econnreset/i
  ];

  const errorMessage = error.message || error.toString();
  return networkErrorPatterns.some(pattern => pattern.test(errorMessage));
};

// Check if device is likely offline
export const isLikelyOffline = (error) => {
  return !navigator.onLine || isNetworkError(error);
};

// Get user-friendly error message based on error type
export const getUserFriendlyErrorMessage = (error) => {
  if (!error) return 'An unknown error occurred';

  // If it's already a user-friendly message, return it
  if (typeof error === 'string') return error;

  // Check for specific error types
  if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
    return 'The request took too long to complete. Please try again.';
  }
  
  if (isNetworkError(error)) {
    return 'Unable to connect to the server. Please check your internet connection.';
  }
  
  if (error.response?.status === 401) {
    return 'Your session has expired. Please log in again.';
  }
  
  if (error.response?.status === 403) {
    return 'You don\'t have permission to perform this action.';
  }
  
  if (error.response?.status >= 400 && error.response?.status < 500) {
    return error.response?.data?.message || 'Please check your input and try again.';
  }
  
  if (error.response?.status >= 500) {
    return 'Something went wrong on our end. Please try again later.';
  }
  
  return error.message || 'An unexpected error occurred. Please try again.';
};

// Retry configuration based on error type
export const getRetryConfig = (error) => {
  const defaultConfig = { shouldRetry: false, maxRetries: 0, delay: 1000 };

  if (!error) return defaultConfig;

  // Don't retry client errors (4xx)
  if (error.response?.status >= 400 && error.response?.status < 500) {
    return defaultConfig;
  }

  // Retry network errors and server errors
  if (isNetworkError(error) || error.response?.status >= 500) {
    return {
      shouldRetry: true,
      maxRetries: 3,
      delay: 1000
    };
  }

  // Retry timeout errors with longer delay
  if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
    return {
      shouldRetry: true,
      maxRetries: 2,
      delay: 2000
    };
  }
  
  return defaultConfig;
};

// Create a delay for retry attempts
export const createRetryDelay = (attempt, baseDelay = 1000) => {
  return new Promise(resolve => {
    const delay = Math.min(baseDelay * Math.pow(2, attempt), 10000); // Max 10 seconds
    setTimeout(resolve, delay);
  });
};

// Check if the API endpoint is reachable
export const checkApiHealth = async (baseUrl) => {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const response = await fetch(`${baseUrl}/`, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    return false;
  }
};

// Monitor network status changes
export const createNetworkMonitor = (onStatusChange) => {
  const handleOnline = () => onStatusChange(true);
  const handleOffline = () => onStatusChange(false);

  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);

  // Return cleanup function
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
};

// Debounce function for network status changes
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};
