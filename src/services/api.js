import axios from 'axios';
import { SecureTokenManager, CSRFManager, sanitizeInput } from '../utils/securityUtils.js';

// Get secure token manager instance
const tokenManager = SecureTokenManager.getInstance();

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Enable cookies for CSRF protection
});

// Request interceptor for adding auth tokens and CSRF protection
api.interceptors.request.use(
  async (config) => {
    // Temporarily disable auth and CSRF for debugging
    console.log('Making API request to:', config.url);

    // Add auth token (disabled for now)
    // const token = tokenManager.getToken();
    // if (token && config.headers) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }

    // Add CSRF token for state-changing requests (disabled for now)
    // if (['post', 'put', 'patch', 'delete'].includes(config.method?.toLowerCase() || '')) {
    //   const csrfToken = await CSRFManager.getCSRFToken();
    //   if (csrfToken && config.headers) {
    //     config.headers['X-CSRF-Token'] = csrfToken;
    //   }
    // }

    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors and token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Handle authentication errors
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      // Try to refresh the token
      const refreshToken = tokenManager.getRefreshToken();
      if (refreshToken) {
        try {
          const response = await api.post('/auth/refresh', {
            refresh_token: refreshToken
          });

          const { access_token, refresh_token: newRefreshToken } = response.data;
          tokenManager.setTokens(access_token, newRefreshToken);

          // Retry the original request with new token
          if (originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${access_token}`;
          }
          return api(originalRequest);
        } catch (refreshError) {
          // Refresh failed, clear tokens and redirect to login
          tokenManager.clearTokens();
          CSRFManager.clearCSRFToken();
          window.dispatchEvent(new CustomEvent('authRequired'));
        }
      } else {
        // No refresh token, clear everything
        tokenManager.clearTokens();
        CSRFManager.clearCSRFToken();
        window.dispatchEvent(new CustomEvent('authRequired'));
      }
    }

    // Handle CSRF token errors
    if (error.response?.status === 403 && error.response?.data?.error === 'CSRF token mismatch') {
      CSRFManager.clearCSRFToken();
      // Retry the request once with a new CSRF token
      if (!originalRequest._csrfRetry) {
        originalRequest._csrfRetry = true;
        return api(originalRequest);
      }
    }

    return Promise.reject(error);
  }
);

// Chat API functions
export const chatAPI = {
  // Get all chats for the user
  getChats: async (includeArchived = false) => {
    const response = await api.get(`/chats?include_archived=${includeArchived}`);
    return response.data;
  },

  // Get archived chats
  getArchivedChats: async () => {
    const response = await api.get('/chats/archived');
    return response.data;
  },

  // Create a new chat
  createChat: async (title = 'New Chat') => {
    const sanitizedTitle = sanitizeInput(title);
    const response = await api.post('/chats', { title: sanitizedTitle });
    return response.data;
  },

  // Get specific chat with messages
  getChat: async (chatId) => {
    const response = await api.get(`/chats/${chatId}`);
    return response.data;
  },

  // Update chat title
  updateChat: async (chatId, updates) => {
    const sanitizedUpdates = {
      ...updates,
      title: updates.title ? sanitizeInput(updates.title) : updates.title
    };
    const response = await api.put(`/chats/${chatId}`, sanitizedUpdates);
    return response.data;
  },

  // Delete chat
  deleteChat: async (chatId) => {
    const response = await api.delete(`/chats/${chatId}`);
    return response.data;
  },

  // Archive chat
  archiveChat: async (chatId) => {
    const response = await api.post(`/chats/${chatId}/archive`);
    return response.data;
  },

  // Unarchive chat
  unarchiveChat: async (chatId) => {
    const response = await api.post(`/chats/${chatId}/unarchive`);
    return response.data;
  },

  // Send message and get AI response
  sendMessage: async (chatId, message) => {
    const sanitizedMessage = sanitizeInput(message);
    const response = await api.post(`/chats/${chatId}/messages`, {
      message: sanitizedMessage,
      role: 'user'
    });
    return response.data;
  },

  // Get messages for a chat
  getMessages: async (chatId) => {
    const response = await api.get(`/chats/${chatId}/messages`);
    return response.data;
  }
};

// Database API functions
export const databaseAPI = {
  // Get all available database tables
  getTables: async () => {
    const response = await api.get('/database/tables');
    return response.data;
  },

  // Get table schema (columns, types, etc.)
  getTableSchema: async (tableName) => {
    const response = await api.get(`/database/tables/${tableName}/schema`);
    return response.data;
  },

  // Execute natural language query
  executeQuery: async (query, selectedTables = []) => {
    const sanitizedQuery = sanitizeInput(query);
    const response = await api.post('/database/query', {
      query: sanitizedQuery,
      selected_tables: selectedTables
    });
    return response.data;
  },

  // Get sample data from a table
  getSampleData: async (tableName, limit = 10) => {
    const response = await api.get(`/database/tables/${tableName}/sample?limit=${limit}`);
    return response.data;
  }
};

// Health check
export const healthAPI = {
  check: async () => {
    const response = await api.get('/');
    return response.data;
  }
};

// Export the axios instance for direct use if needed
export default api;
