import { useState, useEffect } from 'react';
import './App.css';
import ChatSidebar from './components/ChatSidebar.jsx';
import DatabaseSidebar from './components/DatabaseSidebar.jsx';
import ChatWindow from './components/ChatWindow.jsx';
import { useChat, useDatabase } from './hooks/useApi.js';
import { aiClassifier } from './utils/aiMessageClassifier.js';

const App = () => {
  // Chat functionality
  const {
    loading: chatLoading,
    chats,
    currentChat,
    setCurrentChat,
    messages,
    loadChats,
    createChat,
    updateChatTitle,
    deleteChat,
    archiveChat,
    unarchiveChat,
    loadMessages,
    sendMessage,
    error: chatError
  } = useChat();

  // Database functionality
  const {
    loading: dbLoading,
    tables: dbTables,
    loadTables,
    error: dbError
  } = useDatabase();

  // Local state for selected table (not in hook)
  const [selectedTable, setSelectedTable] = useState(null);

  // Message input state
  const [prompt, setPrompt] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [queryResult, setQueryResult] = useState(null);
  const [showQueryResult, setShowQueryResult] = useState(false);

  // Load initial data
  useEffect(() => {
    // Load chats and database tables on app start
    const initializeApp = async () => {
      try {
        await Promise.all([
          loadChats(false), // Load active chats
          loadTables()      // Load database tables
        ]);
      } catch (error) {
        console.error('Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, [loadChats, loadTables]);

  // Auto-select first chat when chats are loaded
  useEffect(() => {
    if (chats.length > 0 && !currentChat) {
      const firstChat = chats[0];
      setCurrentChat(firstChat);
      loadMessages(firstChat.id);
    }
  }, [chats, currentChat, loadMessages, setCurrentChat]);

  // Handle sending messages
  const handleSend = async () => {
    if (!prompt.trim() || isSubmitting) return;

    console.log('Starting handleSend with prompt:', prompt);
    setIsSubmitting(true);
    const messageToSend = prompt.trim();

    try {
      let chatToUse = currentChat;
      let isNewChat = false;

      console.log('Current chat:', currentChat);

      // If no chat is selected, create a new one first
      if (!chatToUse) {
        console.log('Creating new chat...');
        chatToUse = await createChat('New Chat', true);
        if (!chatToUse) {
          throw new Error('Failed to create new chat');
        }
        console.log('New chat created:', chatToUse);
        // Load messages for the new chat (will be empty initially)
        await loadMessages(chatToUse.id);
        isNewChat = true;
      }

      console.log('Sending message to chat:', chatToUse.id);
      const response = await sendMessage(chatToUse.id, messageToSend);
      console.log('Message response:', response);

      if (!response) {
        throw new Error('Failed to send message');
      }

      setPrompt('');

      // If this is a new chat and we have a response, update the title based on the first message
      if (isNewChat && messageToSend) {
        const title = messageToSend.length > 50
          ? messageToSend.substring(0, 47) + '...'
          : messageToSend;
        console.log('Updating chat title to:', title);
        await updateChatTitle(chatToUse.id, title);
      }

      // Use AI-powered classification instead of hardcoded patterns
      // This completely replaces all hardcoded conversational phrase detection

      try {
        // Use AI classifier to determine if query modal should be shown
        const shouldShowModal = await aiClassifier.shouldShowQueryModal(messageToSend, response);

        if (shouldShowModal) {
          if (response.query_result) {
            setQueryResult(response.query_result);
            setShowQueryResult(true);
          } else if (response.sql_query) {
            setQueryResult({ sql_query: response.sql_query });
            setShowQueryResult(true);
          }
        }
      } catch (error) {
        console.warn('AI classification failed, using fallback logic:', error);
        // Fallback: show modal if response has query data and message looks like a query
        const hasQueryData = response.query_result || response.sql_query;
        const looksLikeQuery = messageToSend.toLowerCase().includes('show') ||
                              messageToSend.toLowerCase().includes('list') ||
                              messageToSend.toLowerCase().includes('count');

        if (hasQueryData && looksLikeQuery) {
          if (response.query_result) {
            setQueryResult(response.query_result);
            setShowQueryResult(true);
          } else if (response.sql_query) {
            setQueryResult({ sql_query: response.sql_query });
            setShowQueryResult(true);
          }
        }
      }

      console.log('Message sent successfully');
    } catch (error) {
      console.error('Failed to send message:', error);
      // Show error to user
      alert(`Error: ${error.message || 'Failed to send message'}`);
      // Reset prompt on error so user can try again
      setPrompt(messageToSend);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle creating new chat
  const handleCreateNewChat = async () => {
    try {
      await createChat();
    } catch (error) {
      console.error('Failed to create new chat:', error);
    }
  };

  // Get current chat messages
  const displayMessages = currentChat ? (messages[currentChat.id] || []) : [];

  return (
    <div className="app">
      {(chatError || dbError) && (
        <div className="error-banner">
          {chatError && <div>Chat Error: {chatError}</div>}
          {dbError && <div>Database Error: {dbError}</div>}
        </div>
      )}

      <div className="app-content">
        <ChatSidebar
          chatLoading={chatLoading}
          chats={chats}
          currentChat={currentChat}
          setCurrentChat={setCurrentChat}
          loadMessages={loadMessages}
          createNewChat={handleCreateNewChat}
          updateChatTitle={updateChatTitle}
          deleteChat={deleteChat}
          archiveChat={archiveChat}
          unarchiveChat={unarchiveChat}
        />

        <main className="main-content">
          <ChatWindow
            currentChat={currentChat}
            displayMessages={displayMessages}
            isSubmitting={isSubmitting}
            prompt={prompt}
            setPrompt={setPrompt}
            handleSend={handleSend}
            queryResult={queryResult}
            showQueryResult={showQueryResult}
            setShowQueryResult={setShowQueryResult}
          />
        </main>

        <DatabaseSidebar
          dbLoading={dbLoading}
          dbTables={dbTables}
          selectedTable={selectedTable}
          setSelectedTable={setSelectedTable}
        />
      </div>
    </div>
  );
};

export default App;