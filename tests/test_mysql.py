#!/usr/bin/env python3
"""
MySQL Database Testing Script

This script tests the MySQL database configuration and management endpoints.
"""

import requests
import json
import sys
import os
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")

def print_section(title):
    """Print a formatted section header."""
    print(f"\n{'='*50}")
    print(f"🗄️  {title}")
    print('='*50)

def print_result(test_name, success, details=None):
    """Print test result."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} {test_name}")
    if details:
        print(f"   Details: {details}")

def get_auth_token():
    """Get authentication token for testing."""
    print_section("Authentication Setup")
    
    # Try to login with test user
    login_data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print_result("Authentication", True, "Login successful")
            return data["access_token"]
        else:
            print_result("Authentication", False, f"Login failed: {response.status_code}")
            return None
            
    except Exception as e:
        print_result("Authentication", False, str(e))
        return None

def test_mysql_health():
    """Test MySQL health endpoint (no auth required)."""
    print_section("MySQL Health Check")
    
    try:
        response = requests.get(f"{BASE_URL}/database/mysql/health")
        success = response.status_code == 200
        
        if success:
            data = response.json()
            print_result("MySQL Health", True, f"Status: {data['status']}")
            if data["status"] == "success":
                print(f"   MySQL Version: {data.get('mysql_version', 'Unknown')}")
            else:
                print(f"   Error: {data.get('error', 'Unknown error')}")
        else:
            print_result("MySQL Health", False, f"HTTP {response.status_code}")
        
        return success and response.json().get("status") == "success"
        
    except Exception as e:
        print_result("MySQL Health", False, str(e))
        return False

def test_mysql_status(auth_token):
    """Test MySQL status endpoint (requires auth)."""
    print_section("MySQL Status Check")
    
    if not auth_token:
        print_result("MySQL Status", False, "No authentication token")
        return False
    
    try:
        headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{BASE_URL}/database/mysql/status", headers=headers)
        success = response.status_code == 200
        
        if success:
            data = response.json()
            print_result("MySQL Status", True, "Status retrieved successfully")
            
            # Print connection info
            connection = data.get("connection", {})
            if connection.get("status") == "success":
                print(f"   MySQL Version: {connection.get('mysql_version')}")
                print(f"   Connection ID: {connection.get('connection_id')}")
                print(f"   Current DB: {connection.get('current_database')}")
            
            # Print database info
            db_info = data.get("database_info", {})
            if "error" not in db_info:
                print(f"   Database: {db_info.get('database_name')}")
                print(f"   Size: {db_info.get('size_mb', 0)} MB")
                print(f"   Tables: {db_info.get('table_count', 0)}")
                print(f"   Connections: {db_info.get('active_connections', 0)}")
        else:
            print_result("MySQL Status", False, f"HTTP {response.status_code}")
            if response.status_code == 403:
                print("   Note: This endpoint requires superuser privileges")
        
        return success
        
    except Exception as e:
        print_result("MySQL Status", False, str(e))
        return False

def test_mysql_optimization(auth_token):
    """Test MySQL optimization endpoint."""
    print_section("MySQL Optimization Test")
    
    if not auth_token:
        print_result("MySQL Optimization", False, "No authentication token")
        return False
    
    try:
        headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(f"{BASE_URL}/database/mysql/optimize", headers=headers)
        success = response.status_code == 200
        
        if success:
            data = response.json()
            print_result("MySQL Optimization", True, "Optimization completed")
            print(f"   Optimized Tables: {data.get('optimized_tables', 0)}")
            print(f"   Failed Tables: {data.get('failed_tables', 0)}")
        else:
            print_result("MySQL Optimization", False, f"HTTP {response.status_code}")
            if response.status_code == 403:
                print("   Note: This endpoint requires superuser privileges")
        
        return success
        
    except Exception as e:
        print_result("MySQL Optimization", False, str(e))
        return False

def test_mysql_backup(auth_token):
    """Test MySQL backup endpoint."""
    print_section("MySQL Backup Test")
    
    if not auth_token:
        print_result("MySQL Backup", False, "No authentication token")
        return False
    
    try:
        headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json"
        }
        
        # Test backup creation
        backup_name = f"test_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
        response = requests.post(
            f"{BASE_URL}/database/mysql/backup",
            params={"backup_name": backup_name},
            headers=headers
        )
        
        success = response.status_code == 200
        
        if success:
            data = response.json()
            print_result("MySQL Backup", True, "Backup created successfully")
            print(f"   Backup File: {data.get('backup_path')}")
            print(f"   Backup Size: {data.get('backup_size_mb', 0)} MB")
        else:
            print_result("MySQL Backup", False, f"HTTP {response.status_code}")
            if response.status_code == 403:
                print("   Note: This endpoint requires superuser privileges")
        
        return success
        
    except Exception as e:
        print_result("MySQL Backup", False, str(e))
        return False

def test_database_tables():
    """Test database tables endpoint."""
    print_section("Database Tables Test")
    
    try:
        response = requests.get(f"{BASE_URL}/database/tables")
        success = response.status_code == 200
        
        if success:
            data = response.json()
            print_result("Database Tables", True, f"Found {len(data)} tables")
            for table in data[:5]:  # Show first 5 tables
                print(f"   - {table.get('name', 'Unknown')}: {table.get('column_count', 0)} columns")
        else:
            print_result("Database Tables", False, f"HTTP {response.status_code}")
        
        return success
        
    except Exception as e:
        print_result("Database Tables", False, str(e))
        return False

def test_database_query():
    """Test database query endpoint."""
    print_section("Database Query Test")
    
    try:
        query_data = {
            "query": "Show me all users",
            "selected_tables": []
        }
        
        response = requests.post(
            f"{BASE_URL}/database/query",
            json=query_data,
            headers={"Content-Type": "application/json"}
        )
        
        success = response.status_code == 200
        
        if success:
            data = response.json()
            print_result("Database Query", True, "Query processed successfully")
            print(f"   SQL Generated: {data.get('sql_query', 'None')[:100]}...")
        else:
            print_result("Database Query", False, f"HTTP {response.status_code}")
        
        return success
        
    except Exception as e:
        print_result("Database Query", False, str(e))
        return False

def main():
    """Run all MySQL tests."""
    print("🗄️  MySQL Database Test Suite")
    print(f"Testing against: {BASE_URL}")
    print(f"Test time: {datetime.now()}")
    
    # Test results
    results = []
    
    # 1. Health check (no auth)
    health_ok = test_mysql_health()
    results.append(health_ok)
    
    # 2. Get authentication token
    auth_token = get_auth_token()
    
    # 3. MySQL status (requires auth)
    if auth_token:
        results.append(test_mysql_status(auth_token))
        results.append(test_mysql_optimization(auth_token))
        results.append(test_mysql_backup(auth_token))
    else:
        print("⚠️  Skipping authenticated tests (no token)")
        results.extend([False, False, False])
    
    # 4. Database functionality tests
    results.append(test_database_tables())
    results.append(test_database_query())
    
    # Summary
    print_section("Test Summary")
    passed = sum(results)
    total = len(results)
    
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if health_ok:
        print("✅ MySQL database is accessible and healthy")
    else:
        print("❌ MySQL database connection issues detected")
    
    if passed >= total * 0.7:  # 70% pass rate
        print("🎉 MySQL setup appears to be working correctly!")
        return 0
    else:
        print("⚠️  Some tests failed. Check configuration and permissions.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
