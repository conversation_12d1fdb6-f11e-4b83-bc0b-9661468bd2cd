#!/usr/bin/env python3
"""
Integration test suite for all architectural changes.
Tests that all components work together correctly after logical deduplication.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Import all components
from models import Base, Tenant, TenantUser, AdminUser
from crud import get_tenant_crud, get_tenant_user_crud, get_admin_user_crud
from query_agent import DatabaseConnectionManager, run_nl_to_sql, get_database_tables
from main import SQLQueryProcessor

# Test database setup
TEST_DATABASE_URL = "sqlite:///./test_integration.db"
test_engine = create_engine(TEST_DATABASE_URL, connect_args={"check_same_thread": False})
TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)

def setup_test_db():
    """Create test database tables."""
    Base.metadata.create_all(bind=test_engine)

def teardown_test_db():
    """Drop test database tables."""
    Base.metadata.drop_all(bind=test_engine)

def get_test_db():
    """Get test database session."""
    return TestSessionLocal()

class TestIntegration:
    """Integration tests for all components."""
    
    def setup_method(self):
        """Setup for each test method."""
        setup_test_db()
        self.db = get_test_db()
    
    def teardown_method(self):
        """Cleanup after each test method."""
        self.db.close()
        teardown_test_db()
    
    def test_end_to_end_tenant_workflow(self):
        """Test complete tenant creation and management workflow."""
        # 1. Create tenant using new CRUD architecture
        tenant_crud = get_tenant_crud()
        tenant = tenant_crud.create_tenant(
            db=self.db,
            employer_name="Integration Test Company",
            email="<EMAIL>",
            phone="555-0199",
            size="large"
        )
        
        # Verify tenant was created with all mixin fields
        assert tenant.id is not None
        assert tenant.employer_name == "Integration Test Company"
        assert tenant.email == "<EMAIL>"
        assert tenant.status == "active"  # From StatusMixin
        assert tenant.created_at is not None  # From TimestampMixin
        assert tenant.updated_at is not None  # From TimestampMixin
        
        # 2. Create tenant user using new CRUD architecture
        tenant_user_crud = get_tenant_user_crud()
        tenant_user = tenant_user_crud.create_tenant_user(
            db=self.db,
            name="Integration Test User",
            role="admin",
            email="<EMAIL>",
            tenant_id=tenant.id,
            password="securepassword123"
        )
        
        # Verify tenant user was created with all mixin fields and password hashing
        assert tenant_user.id is not None
        assert tenant_user.name == "Integration Test User"
        assert tenant_user.email == "<EMAIL>"
        assert tenant_user.status == "active"  # From StatusMixin
        assert tenant_user.created_at is not None  # From TimestampMixin
        assert tenant_user.hashed_password != "securepassword123"  # Should be hashed
        assert tenant_user.tenant_id == tenant.id
        
        # 3. Test generic CRUD operations
        retrieved_tenant = tenant_crud.get_by_id(self.db, tenant.id)
        assert retrieved_tenant.id == tenant.id
        
        retrieved_user = tenant_user_crud.get_by_email(self.db, "<EMAIL>")
        assert retrieved_user.id == tenant_user.id
        
        print("✅ End-to-end tenant workflow test passed")
    
    def test_sql_processing_integration(self):
        """Test SQL processing with database connection manager integration."""
        # Test greeting processing
        greeting_result = SQLQueryProcessor.process_natural_language_query("hello", "chat")
        assert greeting_result["success"] == True
        assert greeting_result["message_type"] == "greeting"
        assert greeting_result["sql_query"] is None
        
        # Test SQL generation with fallback
        sql_result = SQLQueryProcessor.process_natural_language_query(
            "show me all users", 
            "chat"
        )
        assert sql_result["success"] == True
        assert sql_result["message_type"] == "sql_generation"
        assert sql_result["sql_query"] is not None
        assert isinstance(sql_result["sql_query"], str)
        
        # Test database tables retrieval with fallback
        tables = get_database_tables("invalid://uri")
        assert isinstance(tables, list)
        assert len(tables) >= 3
        
        # Verify consistency between components
        mock_tables = DatabaseConnectionManager.get_mock_tables()
        assert len(tables) == len(mock_tables)
        
        print("✅ SQL processing integration test passed")
    
    def test_model_crud_integration(self):
        """Test that models work seamlessly with CRUD operations."""
        # Create entities using different CRUD classes
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()
        admin_user_crud = get_admin_user_crud()
        
        # Create tenant
        tenant = tenant_crud.create_entity(
            db=self.db,
            employer_name="CRUD Integration Test",
            email="<EMAIL>"
        )
        
        # Create tenant user
        tenant_user = tenant_user_crud.create_entity(
            db=self.db,
            name="CRUD User",
            role="analyst",
            email="<EMAIL>",
            tenant_id=tenant.id,
            password="password123"
        )
        
        # Create admin user
        admin_user = admin_user_crud.create_entity(
            db=self.db,
            name="CRUD Admin",
            role="admin",
            email="<EMAIL>",
            password="adminpass123"
        )
        
        # Test that all entities have consistent behavior from mixins
        entities = [tenant, tenant_user, admin_user]
        for entity in entities:
            # All should have BaseModel methods
            assert hasattr(entity, 'to_dict')
            assert hasattr(entity, 'id')
            assert entity.id is not None
            
            # All should have TimestampMixin fields
            assert hasattr(entity, 'created_at')
            assert hasattr(entity, 'updated_at')
            assert entity.created_at is not None
            assert entity.updated_at is not None
            
            # Test to_dict method
            entity_dict = entity.to_dict()
            assert isinstance(entity_dict, dict)
            assert 'id' in entity_dict
            assert 'created_at' in entity_dict
            assert 'updated_at' in entity_dict
        
        # Test status mixin for applicable entities
        status_entities = [tenant, tenant_user, admin_user]
        for entity in status_entities:
            if hasattr(entity, 'status'):
                assert entity.status == 'active'
        
        print("✅ Model-CRUD integration test passed")
    
    def test_password_hashing_consistency(self):
        """Test that password hashing is consistent across all user types."""
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()
        admin_user_crud = get_admin_user_crud()
        
        # Create tenant first
        tenant = tenant_crud.create_tenant(
            db=self.db,
            employer_name="Password Test Company",
            email="<EMAIL>"
        )
        
        # Test password hashing for tenant user
        tenant_user = tenant_user_crud.create_tenant_user(
            db=self.db,
            name="Password User",
            role="admin",
            email="<EMAIL>",
            tenant_id=tenant.id,
            password="testpassword"
        )
        
        # Test password hashing for admin user
        admin_user = admin_user_crud.create_admin_user(
            db=self.db,
            name="Password Admin",
            role="super_admin",
            email="<EMAIL>",
            password="testpassword"
        )
        
        # Both should have hashed passwords
        assert tenant_user.hashed_password != "testpassword"
        assert admin_user.password != "testpassword"
        
        # Hashes should be different even for same password (due to salt)
        assert tenant_user.hashed_password != admin_user.password
        
        # Both should be valid bcrypt hashes
        assert tenant_user.hashed_password.startswith('$2b$')
        assert admin_user.password.startswith('$2b$')
        
        print("✅ Password hashing consistency test passed")
    
    def test_error_handling_consistency(self):
        """Test that error handling is consistent across all components."""
        # Test CRUD error handling
        tenant_crud = get_tenant_crud()
        
        # Test getting non-existent entity
        non_existent = tenant_crud.get_by_id(self.db, 99999)
        assert non_existent is None
        
        # Test database connection error handling
        invalid_uri = "invalid://database/uri"
        
        # All these should handle errors gracefully
        sql_result = run_nl_to_sql(invalid_uri, "test query")
        tables_result = get_database_tables(invalid_uri)
        
        # Should return fallback data, not raise exceptions
        assert sql_result is not None
        assert tables_result is not None
        assert isinstance(tables_result, list)
        
        # Test SQL processor error handling
        processor_result = SQLQueryProcessor.process_natural_language_query(
            "test query",
            "chat"
        )
        assert processor_result["success"] == True  # Should handle gracefully
        
        print("✅ Error handling consistency test passed")

def run_integration_tests():
    """Run all integration tests."""
    print("🧪 Starting Integration Tests...")
    
    test_instance = TestIntegration()
    
    try:
        test_instance.setup_method()
        test_instance.test_end_to_end_tenant_workflow()
        test_instance.teardown_method()
        
        test_instance.setup_method()
        test_instance.test_sql_processing_integration()
        test_instance.teardown_method()
        
        test_instance.setup_method()
        test_instance.test_model_crud_integration()
        test_instance.teardown_method()
        
        test_instance.setup_method()
        test_instance.test_password_hashing_consistency()
        test_instance.teardown_method()
        
        test_instance.setup_method()
        test_instance.test_error_handling_consistency()
        test_instance.teardown_method()
        
        print("🎉 All Integration Tests Passed!")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
