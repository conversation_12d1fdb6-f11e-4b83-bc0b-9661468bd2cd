#!/usr/bin/env python3
"""
Test suite for CRUD operations with BaseCRUD.
Tests the logical deduplication improvements in crud.py.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime

# Import models and CRUD components
from models import Base, Tenant, TenantUser, AdminUser
from crud import get_tenant_crud, get_tenant_user_crud, get_admin_user_crud, BaseCRUD
from database import get_db_session

# Test database setup
TEST_DATABASE_URL = "sqlite:///./test_crud.db"
test_engine = create_engine(TEST_DATABASE_URL, connect_args={"check_same_thread": False})
TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)

def setup_test_db():
    """Create test database tables."""
    Base.metadata.create_all(bind=test_engine)

def teardown_test_db():
    """Drop test database tables."""
    Base.metadata.drop_all(bind=test_engine)

def get_test_db():
    """Get test database session."""
    db = TestSessionLocal()
    try:
        return db
    finally:
        pass  # Don't close here, let test handle it

class TestCRUDOperations:
    """Test CRUD operations functionality."""
    
    def setup_method(self):
        """Setup for each test method."""
        setup_test_db()
        self.db = get_test_db()
    
    def teardown_method(self):
        """Cleanup after each test method."""
        self.db.close()
        teardown_test_db()
    
    def test_tenant_crud_inheritance(self):
        """Test that TenantCRUD inherits from BaseCRUD correctly."""
        tenant_crud = get_tenant_crud()
        
        # Check that it's an instance of BaseCRUD
        assert isinstance(tenant_crud, BaseCRUD)
        
        # Check that it has the generic methods
        assert hasattr(tenant_crud, 'create_entity')
        assert hasattr(tenant_crud, 'get_by_id')
        assert hasattr(tenant_crud, 'get_by_email')
        assert hasattr(tenant_crud, 'get_all')
        assert hasattr(tenant_crud, 'update_entity')
        assert hasattr(tenant_crud, 'delete_entity')
        
        # Check that it has the specific methods
        assert hasattr(tenant_crud, 'create_tenant')
        assert hasattr(tenant_crud, 'get_tenant_by_id')
        assert hasattr(tenant_crud, 'get_tenant_by_email')
        
        print("✅ TenantCRUD inheritance test passed")
    
    def test_tenant_crud_operations(self):
        """Test tenant CRUD operations work correctly."""
        tenant_crud = get_tenant_crud()
        
        # Test create
        tenant = tenant_crud.create_tenant(
            db=self.db,
            employer_name="Test Company",
            email="<EMAIL>",
            phone="************",
            size="medium"
        )
        
        assert tenant is not None
        assert tenant.id is not None
        assert tenant.employer_name == "Test Company"
        assert tenant.email == "<EMAIL>"
        assert tenant.phone == "************"
        assert tenant.size == "medium"
        assert tenant.status == "active"  # Default from StatusMixin
        assert tenant.created_at is not None
        assert tenant.updated_at is not None
        
        # Test get by id
        retrieved_tenant = tenant_crud.get_tenant_by_id(self.db, tenant.id)
        assert retrieved_tenant is not None
        assert retrieved_tenant.id == tenant.id
        assert retrieved_tenant.employer_name == tenant.employer_name
        
        # Test get by email
        email_tenant = tenant_crud.get_tenant_by_email(self.db, "<EMAIL>")
        assert email_tenant is not None
        assert email_tenant.id == tenant.id
        
        # Test get all
        all_tenants = tenant_crud.get_tenants(self.db)
        assert len(all_tenants) == 1
        assert all_tenants[0].id == tenant.id
        
        print("✅ TenantCRUD operations test passed")
    
    def test_tenant_user_crud_operations(self):
        """Test tenant user CRUD operations work correctly."""
        # First create a tenant
        tenant_crud = get_tenant_crud()
        tenant = tenant_crud.create_tenant(
            db=self.db,
            employer_name="Test Company",
            email="<EMAIL>"
        )
        
        # Now test tenant user CRUD
        tenant_user_crud = get_tenant_user_crud()
        
        # Test create with password hashing
        tenant_user = tenant_user_crud.create_tenant_user(
            db=self.db,
            name="Test User",
            role="admin",
            email="<EMAIL>",
            tenant_id=tenant.id,
            phone="************",
            password="testpassword123"
        )
        
        assert tenant_user is not None
        assert tenant_user.id is not None
        assert tenant_user.name == "Test User"
        assert tenant_user.role == "admin"
        assert tenant_user.email == "<EMAIL>"
        assert tenant_user.tenant_id == tenant.id
        assert tenant_user.phone == "************"
        assert tenant_user.status == "active"  # Default from StatusMixin
        assert tenant_user.hashed_password is not None
        assert tenant_user.hashed_password != "testpassword123"  # Should be hashed
        assert tenant_user.is_superuser == False
        
        # Test get by id
        retrieved_user = tenant_user_crud.get_tenant_user_by_id(self.db, tenant_user.id)
        assert retrieved_user is not None
        assert retrieved_user.id == tenant_user.id
        
        # Test get by email
        email_user = tenant_user_crud.get_tenant_user_by_email(self.db, "<EMAIL>")
        assert email_user is not None
        assert email_user.id == tenant_user.id
        
        # Test get users by tenant
        tenant_users = tenant_user_crud.get_tenant_users_by_tenant(self.db, tenant.id)
        assert len(tenant_users) == 1
        assert tenant_users[0].id == tenant_user.id
        
        print("✅ TenantUserCRUD operations test passed")
    
    def test_password_hashing_logic(self):
        """Test that password hashing works consistently across all CRUD classes."""
        # Test TenantUser password hashing
        tenant_crud = get_tenant_crud()
        tenant = tenant_crud.create_tenant(
            db=self.db,
            employer_name="Test Company",
            email="<EMAIL>"
        )
        
        tenant_user_crud = get_tenant_user_crud()
        tenant_user = tenant_user_crud.create_tenant_user(
            db=self.db,
            name="Test User",
            role="admin",
            email="<EMAIL>",
            tenant_id=tenant.id,
            password="password123"
        )
        
        # Test AdminUser password hashing
        admin_user_crud = get_admin_user_crud()
        admin_user = admin_user_crud.create_admin_user(
            db=self.db,
            name="Admin User",
            role="super_admin",
            email="<EMAIL>",
            password="adminpass123"
        )
        
        # Check that passwords are hashed
        assert tenant_user.hashed_password != "password123"
        assert admin_user.password != "adminpass123"
        
        # Check that hashed passwords are different even for same input
        tenant_user2 = tenant_user_crud.create_tenant_user(
            db=self.db,
            name="Test User 2",
            role="admin",
            email="<EMAIL>",
            tenant_id=tenant.id,
            password="password123"  # Same password
        )
        
        # Hashes should be different due to salt
        assert tenant_user.hashed_password != tenant_user2.hashed_password
        
        print("✅ Password hashing logic test passed")
    
    def test_generic_crud_methods(self):
        """Test that generic CRUD methods work correctly."""
        tenant_crud = get_tenant_crud()
        
        # Test generic create_entity method
        tenant = tenant_crud.create_entity(
            db=self.db,
            employer_name="Generic Test Company",
            email="<EMAIL>",
            phone="555-0123"
        )
        
        assert tenant is not None
        assert tenant.employer_name == "Generic Test Company"
        assert tenant.email == "<EMAIL>"
        
        # Test generic get_by_id method
        retrieved = tenant_crud.get_by_id(self.db, tenant.id)
        assert retrieved is not None
        assert retrieved.id == tenant.id
        
        # Test generic get_by_email method
        email_retrieved = tenant_crud.get_by_email(self.db, "<EMAIL>")
        assert email_retrieved is not None
        assert email_retrieved.id == tenant.id
        
        # Test generic get_all method
        all_entities = tenant_crud.get_all(self.db)
        assert len(all_entities) >= 1
        
        # Test generic update_entity method
        updated = tenant_crud.update_entity(
            db=self.db,
            entity_id=tenant.id,
            employer_name="Updated Company Name"
        )
        assert updated is not None
        assert updated.employer_name == "Updated Company Name"
        assert updated.email == "<EMAIL>"  # Unchanged
        
        # Test generic delete_entity method
        deleted = tenant_crud.delete_entity(self.db, tenant.id)
        assert deleted == True
        
        # Verify deletion
        not_found = tenant_crud.get_by_id(self.db, tenant.id)
        assert not_found is None
        
        print("✅ Generic CRUD methods test passed")

def run_crud_tests():
    """Run all CRUD tests."""
    print("🧪 Starting CRUD Operations Tests...")
    
    test_instance = TestCRUDOperations()
    
    try:
        test_instance.setup_method()
        test_instance.test_tenant_crud_inheritance()
        test_instance.teardown_method()
        
        test_instance.setup_method()
        test_instance.test_tenant_crud_operations()
        test_instance.teardown_method()
        
        test_instance.setup_method()
        test_instance.test_tenant_user_crud_operations()
        test_instance.teardown_method()
        
        test_instance.setup_method()
        test_instance.test_password_hashing_logic()
        test_instance.teardown_method()
        
        test_instance.setup_method()
        test_instance.test_generic_crud_methods()
        test_instance.teardown_method()
        
        print("🎉 All CRUD Operations Tests Passed!")
        return True
        
    except Exception as e:
        print(f"❌ CRUD test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_crud_tests()
    sys.exit(0 if success else 1)
