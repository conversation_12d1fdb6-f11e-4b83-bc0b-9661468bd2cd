"""
Pytest configuration and shared fixtures for SaaS Backend tests.

This module provides:
- Database fixtures for testing
- Authentication fixtures
- API client fixtures
- Mock fixtures for external services
"""

import os
import pytest
import asyncio
from typing import Generator, Dict, Any
from unittest.mock import Mock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
import httpx

# Set test environment
os.environ["APP_ENV"] = "test"
os.environ["TEST_DATABASE_URL"] = "mysql+pymysql://root:@localhost:3306/saas_test_db"
os.environ["JWT_SECRET_KEY"] = "test-jwt-secret-key-for-testing-only"
os.environ["OPENAI_API_KEY"] = "test-openai-key"
os.environ["LOG_LEVEL"] = "WARNING"
os.environ["ENABLE_RATE_LIMITING"] = "false"
os.environ["SQL_ECHO"] = "false"

# Import after setting environment
from main import app
from database import get_db, Base
from models import Tenant, Tenant<PERSON>ser, AdminUser
from auth import JWTManager, PasswordManager

# Test database configuration
TEST_DATABASE_URL = "mysql+pymysql://root:@localhost:3306/saas_test_db"

@pytest.fixture(scope="session")
def test_engine():
    """Create test database engine."""
    engine = create_engine(
        TEST_DATABASE_URL,
        pool_size=5,
        max_overflow=10,
        pool_pre_ping=True,
        echo=False
    )
    return engine

@pytest.fixture(scope="session")
def test_session_factory(test_engine):
    """Create test session factory."""
    return sessionmaker(autocommit=False, autoflush=False, bind=test_engine)

@pytest.fixture(scope="function")
def db_session(test_engine, test_session_factory):
    """Create a fresh database session for each test."""
    # Create all tables
    Base.metadata.create_all(bind=test_engine)
    
    # Create session
    session = test_session_factory()
    
    try:
        yield session
    finally:
        session.close()
        # Clean up tables after test
        Base.metadata.drop_all(bind=test_engine)

@pytest.fixture(scope="function")
def client(db_session):
    """Create FastAPI test client with database override."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    # Clean up
    app.dependency_overrides.clear()

@pytest.fixture(scope="function")
async def async_client(db_session):
    """Create async HTTP client for testing."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    async with httpx.AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    # Clean up
    app.dependency_overrides.clear()

# ============================================================================
# Authentication Fixtures
# ============================================================================

@pytest.fixture
def test_user_data():
    """Test user data for registration/login."""
    return {
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "full_name": "Test User"
    }

@pytest.fixture
def test_admin_data():
    """Test admin user data."""
    return {
        "email": "<EMAIL>", 
        "password": "AdminPassword123!",
        "full_name": "Test Admin"
    }

@pytest.fixture
def created_user(db_session, test_user_data):
    """Create a test user in the database."""
    from crud import user_crud
    
    user = user_crud.create_user(
        db=db_session,
        email=test_user_data["email"],
        password=test_user_data["password"],
        full_name=test_user_data["full_name"]
    )
    return user

@pytest.fixture
def created_admin_user(db_session, test_admin_data):
    """Create a test admin user in the database."""
    from crud import user_crud
    
    admin = user_crud.create_user(
        db=db_session,
        email=test_admin_data["email"],
        password=test_admin_data["password"],
        full_name=test_admin_data["full_name"],
        is_superuser=True
    )
    return admin

@pytest.fixture
def user_token(created_user):
    """Generate JWT token for test user."""
    token_data = {
        "sub": created_user.id,
        "email": created_user.email,
        "full_name": created_user.full_name,
        "is_superuser": False
    }
    return JWTManager.create_access_token(token_data)

@pytest.fixture
def admin_token(created_admin_user):
    """Generate JWT token for admin user."""
    token_data = {
        "sub": created_admin_user.id,
        "email": created_admin_user.email,
        "full_name": created_admin_user.full_name,
        "is_superuser": True
    }
    return JWTManager.create_access_token(token_data)

@pytest.fixture
def auth_headers(user_token):
    """Create authorization headers for API requests."""
    return {"Authorization": f"Bearer {user_token}"}

@pytest.fixture
def admin_headers(admin_token):
    """Create admin authorization headers for API requests."""
    return {"Authorization": f"Bearer {admin_token}"}

# ============================================================================
# Mock Fixtures
# ============================================================================

@pytest.fixture
def mock_openai():
    """Mock OpenAI API calls."""
    with patch('openai.OpenAI') as mock_client:
        # Mock chat completion
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message = Mock()
        mock_response.choices[0].message.content = "SELECT * FROM users;"
        
        mock_client.return_value.chat.completions.create.return_value = mock_response
        yield mock_client

@pytest.fixture
def mock_mysql_manager():
    """Mock MySQL manager for tests that don't need real database."""
    with patch('mysql_manager.get_mysql_manager') as mock_manager:
        mock_instance = Mock()
        mock_instance.test_connection.return_value = {
            "status": "success",
            "mysql_version": "8.0.0-test",
            "connection_id": 123,
            "current_database": "saas_test_db"
        }
        mock_instance.get_database_info.return_value = {
            "database_name": "saas_test_db",
            "size_mb": 10.5,
            "table_count": 5,
            "active_connections": 1
        }
        mock_manager.return_value = mock_instance
        yield mock_instance

# ============================================================================
# Data Fixtures
# ============================================================================

@pytest.fixture
def sample_tenant_data():
    """Sample tenant data for testing."""
    return {
        "employer_name": "Test Company Inc.",
        "email": "<EMAIL>",
        "phone": "******-123-4567",
        "address": "123 Test Street, Test City, TC 12345"
    }

@pytest.fixture
def sample_message_data():
    """Sample message data for AI testing."""
    return {
        "message": "Show me all users in the database",
        "selected_tables": ["users", "tenants"]
    }

@pytest.fixture
def invalid_message_data():
    """Invalid message data for testing error handling."""
    return {
        "message": "",  # Empty message
        "selected_tables": []
    }

# ============================================================================
# Utility Fixtures
# ============================================================================

@pytest.fixture
def cleanup_files():
    """Clean up test files after test completion."""
    files_to_cleanup = []
    
    def add_file(filepath):
        files_to_cleanup.append(filepath)
    
    yield add_file
    
    # Cleanup
    for filepath in files_to_cleanup:
        if os.path.exists(filepath):
            os.remove(filepath)

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

# ============================================================================
# Test Markers
# ============================================================================

def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "api: mark test as an API test"
    )
    config.addinivalue_line(
        "markers", "database: mark test as a database test"
    )
    config.addinivalue_line(
        "markers", "auth: mark test as an authentication test"
    )
    config.addinivalue_line(
        "markers", "ai: mark test as an AI integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "security: mark test as a security test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as a performance test"
    )
