#!/usr/bin/env python3
"""
Test suite for SQL Query Processor.
Tests the logical deduplication improvements in main.py SQLQueryProcessor.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import SQL processor components
from main import SQLQueryProcessor

class TestSQLQueryProcessor:
    """Test SQL Query Processor functionality."""
    
    def test_greeting_detection(self):
        """Test that greeting detection works correctly."""
        # Test positive cases (short greetings only)
        greeting_messages = [
            "hi",
            "hello",
            "hey",
            "Hi there!",
            "hi there",
            "hello there",
            "good morning",
            "good afternoon",
            "greetings"
        ]
        
        for message in greeting_messages:
            result = SQLQueryProcessor.is_greeting_or_welcome(message)
            assert result == True, f"'{message}' should be detected as greeting"
        
        # Test negative cases
        non_greeting_messages = [
            "show me all users",
            "select * from orders",
            "what is the total revenue?",
            "how many customers do we have?",
            "generate a report",
            "hello world this is a long query about database tables",
            "Hello world",  # More than 2 words
            "Hey, how are you?",  # More than 2 words
        ]
        
        for message in non_greeting_messages:
            result = SQLQueryProcessor.is_greeting_or_welcome(message)
            assert result == False, f"'{message}' should NOT be detected as greeting"
        
        print("✅ Greeting detection test passed")
    
    def test_welcome_response_generation(self):
        """Test that welcome responses are generated correctly."""
        # Test chat context
        chat_response = SQLQueryProcessor.generate_welcome_response("chat")
        assert isinstance(chat_response, str)
        assert len(chat_response) > 0
        assert "Hi" in chat_response or "Hello" in chat_response
        
        # Test direct context
        direct_response = SQLQueryProcessor.generate_welcome_response("direct")
        assert isinstance(direct_response, str)
        assert len(direct_response) > 0
        assert "Welcome" in direct_response or "ready" in direct_response
        
        # Test default context
        default_response = SQLQueryProcessor.generate_welcome_response()
        assert isinstance(default_response, str)
        assert len(default_response) > 0
        
        print("✅ Welcome response generation test passed")
    
    def test_process_greeting_query(self):
        """Test processing of greeting queries."""
        greeting_query = "hello"
        
        # Test chat context
        result = SQLQueryProcessor.process_natural_language_query(
            query=greeting_query,
            context="chat"
        )
        
        assert isinstance(result, dict)
        assert result["success"] == True
        assert result["message_type"] == "greeting"
        assert result["sql_query"] is None
        assert "content" in result
        assert len(result["content"]) > 0
        
        # Test direct context
        result_direct = SQLQueryProcessor.process_natural_language_query(
            query=greeting_query,
            context="direct"
        )
        
        assert result_direct["success"] == True
        assert result_direct["message_type"] == "greeting"
        assert result_direct["sql_query"] is None
        
        print("✅ Process greeting query test passed")
    
    def test_process_sql_query(self):
        """Test processing of SQL generation queries."""
        sql_query = "show me all users"
        
        # Test chat context
        result = SQLQueryProcessor.process_natural_language_query(
            query=sql_query,
            context="chat"
        )
        
        assert isinstance(result, dict)
        assert result["success"] == True
        assert result["message_type"] == "sql_generation"
        assert result["sql_query"] is not None
        assert isinstance(result["sql_query"], str)
        assert len(result["sql_query"]) > 0
        assert "content" in result
        assert "SQL query" in result["content"]
        assert "```sql" in result["content"]  # Should have formatted SQL
        
        # Test direct context
        result_direct = SQLQueryProcessor.process_natural_language_query(
            query=sql_query,
            context="direct"
        )
        
        assert result_direct["success"] == True
        assert result_direct["message_type"] == "sql_generation"
        assert result_direct["sql_query"] is not None
        assert "generated successfully" in result_direct["content"]
        
        print("✅ Process SQL query test passed")
    
    def test_error_handling(self):
        """Test that error handling works correctly."""
        # Test with None query (should handle gracefully)
        try:
            result = SQLQueryProcessor.process_natural_language_query(
                query=None,
                context="chat"
            )
            # Should either handle gracefully or return error result
            if not result["success"]:
                assert "error" in result
                assert result["message_type"] == "error"
        except Exception:
            # Exception handling is also acceptable
            pass
        
        print("✅ Error handling test passed")
    
    def test_context_aware_processing(self):
        """Test that processing is context-aware."""
        query = "show me all orders"
        
        # Test different contexts produce different content
        chat_result = SQLQueryProcessor.process_natural_language_query(
            query=query,
            context="chat"
        )
        
        direct_result = SQLQueryProcessor.process_natural_language_query(
            query=query,
            context="direct"
        )
        
        # Both should succeed and generate SQL
        assert chat_result["success"] == True
        assert direct_result["success"] == True
        assert chat_result["sql_query"] is not None
        assert direct_result["sql_query"] is not None
        
        # Content should be different based on context
        assert chat_result["content"] != direct_result["content"]
        assert "Tip" in chat_result["content"]  # Chat has tips
        assert "generated successfully" in direct_result["content"]  # Direct is more formal
        
        print("✅ Context-aware processing test passed")
    
    def test_consistent_behavior(self):
        """Test that processor behavior is consistent."""
        query = "hello"
        
        # Multiple calls should produce consistent results
        result1 = SQLQueryProcessor.process_natural_language_query(query, "chat")
        result2 = SQLQueryProcessor.process_natural_language_query(query, "chat")
        result3 = SQLQueryProcessor.process_natural_language_query(query, "chat")
        
        # All should be greetings
        assert result1["message_type"] == "greeting"
        assert result2["message_type"] == "greeting"
        assert result3["message_type"] == "greeting"
        
        # All should have no SQL
        assert result1["sql_query"] is None
        assert result2["sql_query"] is None
        assert result3["sql_query"] is None
        
        # All should succeed
        assert result1["success"] == True
        assert result2["success"] == True
        assert result3["success"] == True
        
        print("✅ Consistent behavior test passed")
    
    def test_sql_processor_eliminates_duplication(self):
        """Test that SQL processor eliminates code duplication."""
        # Test that both greeting and SQL queries use the same processor
        greeting_result = SQLQueryProcessor.process_natural_language_query("hi", "chat")
        sql_result = SQLQueryProcessor.process_natural_language_query("show users", "chat")
        
        # Both should have the same response structure
        required_keys = ["success", "message_type", "content", "sql_query"]
        
        for key in required_keys:
            assert key in greeting_result, f"Greeting result missing key: {key}"
            assert key in sql_result, f"SQL result missing key: {key}"
        
        # Both should use the same success/error handling
        assert isinstance(greeting_result["success"], bool)
        assert isinstance(sql_result["success"], bool)
        
        # Both should have consistent message types
        assert greeting_result["message_type"] in ["greeting", "sql_generation", "error"]
        assert sql_result["message_type"] in ["greeting", "sql_generation", "error"]
        
        print("✅ SQL processor duplication elimination test passed")

def run_sql_processor_tests():
    """Run all SQL processor tests."""
    print("🧪 Starting SQL Query Processor Tests...")
    
    test_instance = TestSQLQueryProcessor()
    
    try:
        test_instance.test_greeting_detection()
        test_instance.test_welcome_response_generation()
        test_instance.test_process_greeting_query()
        test_instance.test_process_sql_query()
        test_instance.test_error_handling()
        test_instance.test_context_aware_processing()
        test_instance.test_consistent_behavior()
        test_instance.test_sql_processor_eliminates_duplication()
        
        print("🎉 All SQL Query Processor Tests Passed!")
        return True
        
    except Exception as e:
        print(f"❌ SQL processor test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_sql_processor_tests()
    sys.exit(0 if success else 1)
