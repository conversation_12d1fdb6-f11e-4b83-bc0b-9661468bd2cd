"""
Unit tests for authentication module.

Tests JWT token management, password hashing, and authentication utilities.
"""

import pytest
from datetime import datetime, timedelta, timezone
from unittest.mock import patch, Mock
import jwt

from auth import (
    J<PERSON>TManager, PasswordManager, Token, UserLogin, User<PERSON><PERSON>ister,
    AuthRateLimiter, validate_email_format, generate_reset_token
)

class TestPasswordManager:
    """Test password management functionality."""
    
    @pytest.mark.unit
    def test_hash_password(self):
        """Test password hashing."""
        password = "TestPassword123!"
        hashed = PasswordManager.hash_password(password)
        
        assert hashed != password
        assert len(hashed) > 50  # bcrypt hashes are long
        assert hashed.startswith("$2b$")  # bcrypt prefix
    
    @pytest.mark.unit
    def test_verify_password_correct(self):
        """Test password verification with correct password."""
        password = "TestPassword123!"
        hashed = PasswordManager.hash_password(password)
        
        assert PasswordManager.verify_password(password, hashed) is True
    
    @pytest.mark.unit
    def test_verify_password_incorrect(self):
        """Test password verification with incorrect password."""
        password = "TestPassword123!"
        wrong_password = "WrongPassword123!"
        hashed = PasswordManager.hash_password(password)
        
        assert PasswordManager.verify_password(wrong_password, hashed) is False
    
    @pytest.mark.unit
    def test_validate_password_strength_strong(self):
        """Test password strength validation for strong password."""
        strong_password = "StrongPassword123!"
        result = PasswordManager.validate_password_strength(strong_password)
        
        assert result["is_valid"] is True
        assert result["strength"] == "strong"
        assert len(result["errors"]) == 0
    
    @pytest.mark.unit
    def test_validate_password_strength_weak(self):
        """Test password strength validation for weak password."""
        weak_password = "weak"
        result = PasswordManager.validate_password_strength(weak_password)
        
        assert result["is_valid"] is False
        assert result["strength"] == "weak"
        assert len(result["errors"]) > 0
        assert "at least 8 characters" in str(result["errors"])
    
    @pytest.mark.unit
    def test_validate_password_strength_missing_requirements(self):
        """Test password validation with missing requirements."""
        passwords_and_expected_errors = [
            ("password123", "uppercase letter"),
            ("PASSWORD123", "lowercase letter"),
            ("PasswordABC", "number"),
            ("Password123", "special character"),
        ]
        
        for password, expected_error in passwords_and_expected_errors:
            result = PasswordManager.validate_password_strength(password)
            assert result["is_valid"] is False
            assert any(expected_error in error for error in result["errors"])

class TestJWTManager:
    """Test JWT token management functionality."""
    
    @pytest.mark.unit
    def test_create_access_token(self):
        """Test access token creation."""
        data = {"sub": "123", "email": "<EMAIL>"}
        token = JWTManager.create_access_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 100  # JWT tokens are long
        
        # Decode and verify
        decoded = jwt.decode(token, options={"verify_signature": False})
        assert decoded["sub"] == "123"
        assert decoded["email"] == "<EMAIL>"
        assert decoded["type"] == "access"
        assert "exp" in decoded
        assert "iat" in decoded
    
    @pytest.mark.unit
    def test_create_refresh_token(self):
        """Test refresh token creation."""
        data = {"sub": "123", "email": "<EMAIL>"}
        token = JWTManager.create_refresh_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 100
        
        # Decode and verify
        decoded = jwt.decode(token, options={"verify_signature": False})
        assert decoded["sub"] == "123"
        assert decoded["email"] == "<EMAIL>"
        assert decoded["type"] == "refresh"
    
    @pytest.mark.unit
    def test_verify_token_valid(self):
        """Test token verification with valid token."""
        data = {"sub": "123", "email": "<EMAIL>"}
        token = JWTManager.create_access_token(data)
        
        payload = JWTManager.verify_token(token, "access")
        
        assert payload["sub"] == "123"
        assert payload["email"] == "<EMAIL>"
        assert payload["type"] == "access"
    
    @pytest.mark.unit
    def test_verify_token_invalid_type(self):
        """Test token verification with wrong token type."""
        data = {"sub": "123", "email": "<EMAIL>"}
        access_token = JWTManager.create_access_token(data)
        
        with pytest.raises(Exception):  # Should raise HTTPException
            JWTManager.verify_token(access_token, "refresh")
    
    @pytest.mark.unit
    def test_verify_token_expired(self):
        """Test token verification with expired token."""
        data = {"sub": "123", "email": "<EMAIL>"}
        
        # Create token with past expiration
        with patch('auth.datetime') as mock_datetime:
            past_time = datetime.now(timezone.utc) - timedelta(hours=1)
            mock_datetime.now.return_value = past_time
            mock_datetime.fromtimestamp = datetime.fromtimestamp
            
            token = JWTManager.create_access_token(data)
        
        with pytest.raises(Exception):  # Should raise HTTPException
            JWTManager.verify_token(token, "access")
    
    @pytest.mark.unit
    def test_create_token_pair(self):
        """Test creating both access and refresh tokens."""
        data = {"sub": "123", "email": "<EMAIL>"}
        token_pair = JWTManager.create_token_pair(data)
        
        assert isinstance(token_pair, Token)
        assert token_pair.token_type == "bearer"
        assert len(token_pair.access_token) > 100
        assert len(token_pair.refresh_token) > 100
        assert token_pair.expires_in > 0

class TestAuthRateLimiter:
    """Test authentication rate limiting functionality."""
    
    @pytest.mark.unit
    def test_rate_limiter_allows_initial_requests(self):
        """Test that rate limiter allows initial requests."""
        limiter = AuthRateLimiter()
        identifier = "test_user"
        
        # Should allow first few requests
        for i in range(3):
            assert limiter.is_allowed(identifier, max_attempts=5) is True
    
    @pytest.mark.unit
    def test_rate_limiter_blocks_excess_requests(self):
        """Test that rate limiter blocks excess requests."""
        limiter = AuthRateLimiter()
        identifier = "test_user"
        max_attempts = 3
        
        # Use up all attempts
        for i in range(max_attempts):
            assert limiter.is_allowed(identifier, max_attempts=max_attempts) is True
        
        # Next request should be blocked
        assert limiter.is_allowed(identifier, max_attempts=max_attempts) is False
    
    @pytest.mark.unit
    def test_rate_limiter_reset_attempts(self):
        """Test resetting rate limiter attempts."""
        limiter = AuthRateLimiter()
        identifier = "test_user"
        max_attempts = 2
        
        # Use up attempts
        for i in range(max_attempts):
            limiter.is_allowed(identifier, max_attempts=max_attempts)
        
        # Should be blocked
        assert limiter.is_allowed(identifier, max_attempts=max_attempts) is False
        
        # Reset and try again
        limiter.reset_attempts(identifier)
        assert limiter.is_allowed(identifier, max_attempts=max_attempts) is True
    
    @pytest.mark.unit
    def test_rate_limiter_different_identifiers(self):
        """Test that rate limiter treats different identifiers separately."""
        limiter = AuthRateLimiter()
        identifier1 = "user1"
        identifier2 = "user2"
        max_attempts = 2
        
        # Use up attempts for user1
        for i in range(max_attempts):
            limiter.is_allowed(identifier1, max_attempts=max_attempts)
        
        # user1 should be blocked
        assert limiter.is_allowed(identifier1, max_attempts=max_attempts) is False
        
        # user2 should still be allowed
        assert limiter.is_allowed(identifier2, max_attempts=max_attempts) is True

class TestAuthUtilities:
    """Test authentication utility functions."""
    
    @pytest.mark.unit
    def test_validate_email_format_valid(self):
        """Test email validation with valid emails."""
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in valid_emails:
            assert validate_email_format(email) is True
    
    @pytest.mark.unit
    def test_validate_email_format_invalid(self):
        """Test email validation with invalid emails."""
        invalid_emails = [
            "invalid-email",
            "@domain.com",
            "user@",
            "user@domain",
            "user <EMAIL>",
            ""
        ]
        
        for email in invalid_emails:
            assert validate_email_format(email) is False
    
    @pytest.mark.unit
    def test_generate_reset_token(self):
        """Test password reset token generation."""
        token = generate_reset_token()
        
        assert isinstance(token, str)
        assert len(token) > 20  # Should be reasonably long
        assert token.isalnum() or '-' in token or '_' in token  # URL-safe characters

class TestAuthModels:
    """Test authentication Pydantic models."""
    
    @pytest.mark.unit
    def test_user_login_model(self):
        """Test UserLogin model validation."""
        valid_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        login = UserLogin(**valid_data)
        assert login.email == "<EMAIL>"
        assert login.password == "password123"
    
    @pytest.mark.unit
    def test_user_register_model(self):
        """Test UserRegister model validation."""
        valid_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "full_name": "Test User"
        }
        
        register = UserRegister(**valid_data)
        assert register.email == "<EMAIL>"
        assert register.password == "password123"
        assert register.full_name == "Test User"
    
    @pytest.mark.unit
    def test_user_register_model_optional_name(self):
        """Test UserRegister model with optional full_name."""
        valid_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        register = UserRegister(**valid_data)
        assert register.email == "<EMAIL>"
        assert register.password == "password123"
        assert register.full_name is None
    
    @pytest.mark.unit
    def test_token_model(self):
        """Test Token model."""
        token_data = {
            "access_token": "access_token_here",
            "refresh_token": "refresh_token_here",
            "expires_in": 1800
        }
        
        token = Token(**token_data)
        assert token.access_token == "access_token_here"
        assert token.refresh_token == "refresh_token_here"
        assert token.token_type == "bearer"  # Default value
        assert token.expires_in == 1800
