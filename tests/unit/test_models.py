"""
Unit tests for database models.

Tests model creation, validation, relationships, and mixins.
"""

import pytest
from datetime import datetime
from sqlalchemy.exc import IntegrityError

from models import (
    Base, Tenant, TenantUser, AdminUser, Chat, Message,
    TimestampMixin, StatusMixin, EmailMixin, BaseModel
)

class TestModelMixins:
    """Test model mixin functionality."""
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_timestamp_mixin(self, db_session):
        """Test TimestampMixin functionality."""
        tenant = Tenant(
            employer_name="Test Company",
            email="<EMAIL>"
        )
        
        # Before saving
        assert tenant.created_at is None
        assert tenant.updated_at is None
        
        db_session.add(tenant)
        db_session.commit()
        db_session.refresh(tenant)
        
        # After saving
        assert tenant.created_at is not None
        assert tenant.updated_at is not None
        assert isinstance(tenant.created_at, datetime)
        assert isinstance(tenant.updated_at, datetime)
        assert tenant.created_at == tenant.updated_at
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_timestamp_mixin_update(self, db_session):
        """Test that updated_at changes on model update."""
        tenant = Tenant(
            employer_name="Test Company",
            email="<EMAIL>"
        )
        
        db_session.add(tenant)
        db_session.commit()
        db_session.refresh(tenant)
        
        original_updated_at = tenant.updated_at
        
        # Update the model
        tenant.employer_name = "Updated Company"
        db_session.commit()
        db_session.refresh(tenant)
        
        # updated_at should change
        assert tenant.updated_at > original_updated_at
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_status_mixin(self, db_session):
        """Test StatusMixin functionality."""
        tenant = Tenant(
            employer_name="Test Company",
            email="<EMAIL>"
        )
        
        # Default status
        assert tenant.status == "active"
        
        db_session.add(tenant)
        db_session.commit()
        db_session.refresh(tenant)
        
        assert tenant.status == "active"
        
        # Change status
        tenant.status = "inactive"
        db_session.commit()
        db_session.refresh(tenant)
        
        assert tenant.status == "inactive"
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_email_mixin(self, db_session):
        """Test EmailMixin functionality."""
        tenant = Tenant(
            employer_name="Test Company",
            email="<EMAIL>"
        )
        
        db_session.add(tenant)
        db_session.commit()
        db_session.refresh(tenant)
        
        assert tenant.email == "<EMAIL>"
        
        # Test email uniqueness (should be enforced by database)
        duplicate_tenant = Tenant(
            employer_name="Another Company",
            email="<EMAIL>"  # Same email
        )
        
        db_session.add(duplicate_tenant)
        
        with pytest.raises(IntegrityError):
            db_session.commit()

class TestTenantModel:
    """Test Tenant model functionality."""
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_tenant_creation(self, db_session):
        """Test basic tenant creation."""
        tenant = Tenant(
            employer_name="Test Company",
            email="<EMAIL>",
            phone="******-123-4567",
            address="123 Test St"
        )
        
        db_session.add(tenant)
        db_session.commit()
        db_session.refresh(tenant)
        
        assert tenant.id is not None
        assert tenant.employer_name == "Test Company"
        assert tenant.email == "<EMAIL>"
        assert tenant.phone == "******-123-4567"
        assert tenant.address == "123 Test St"
        assert tenant.status == "active"
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_tenant_required_fields(self, db_session):
        """Test that required fields are enforced."""
        # Missing employer_name
        tenant = Tenant(email="<EMAIL>")
        
        db_session.add(tenant)
        
        with pytest.raises(IntegrityError):
            db_session.commit()
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_tenant_to_dict(self, db_session):
        """Test tenant to_dict method."""
        tenant = Tenant(
            employer_name="Test Company",
            email="<EMAIL>"
        )
        
        db_session.add(tenant)
        db_session.commit()
        db_session.refresh(tenant)
        
        tenant_dict = tenant.to_dict()
        
        assert isinstance(tenant_dict, dict)
        assert "id" in tenant_dict
        assert "employer_name" in tenant_dict
        assert "email" in tenant_dict
        assert "status" in tenant_dict
        assert "created_at" in tenant_dict
        assert "updated_at" in tenant_dict
        
        assert tenant_dict["employer_name"] == "Test Company"
        assert tenant_dict["email"] == "<EMAIL>"
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_tenant_repr(self, db_session):
        """Test tenant __repr__ method."""
        tenant = Tenant(
            employer_name="Test Company",
            email="<EMAIL>"
        )
        
        db_session.add(tenant)
        db_session.commit()
        db_session.refresh(tenant)
        
        repr_str = repr(tenant)
        
        assert "Tenant" in repr_str
        assert str(tenant.id) in repr_str

class TestTenantUserModel:
    """Test TenantUser model functionality."""
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_tenant_user_creation(self, db_session, sample_tenant_data):
        """Test basic tenant user creation."""
        # Create tenant first
        tenant = Tenant(**sample_tenant_data)
        db_session.add(tenant)
        db_session.commit()
        db_session.refresh(tenant)
        
        # Create tenant user
        user = TenantUser(
            name="Test User",
            role="admin",
            email="<EMAIL>",
            tenant_id=tenant.id,
            hashed_password="hashed_password_here"
        )
        
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        assert user.id is not None
        assert user.name == "Test User"
        assert user.role == "admin"
        assert user.email == "<EMAIL>"
        assert user.tenant_id == tenant.id
        assert user.status == "active"
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_tenant_user_relationship(self, db_session, sample_tenant_data):
        """Test tenant-user relationship."""
        # Create tenant
        tenant = Tenant(**sample_tenant_data)
        db_session.add(tenant)
        db_session.commit()
        db_session.refresh(tenant)
        
        # Create tenant user
        user = TenantUser(
            name="Test User",
            role="admin",
            email="<EMAIL>",
            tenant_id=tenant.id,
            hashed_password="hashed_password_here"
        )
        
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        # Test relationship
        assert user.tenant is not None
        assert user.tenant.id == tenant.id
        assert user.tenant.employer_name == sample_tenant_data["employer_name"]
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_tenant_user_required_fields(self, db_session):
        """Test that required fields are enforced."""
        # Missing required fields
        user = TenantUser(name="Test User")
        
        db_session.add(user)
        
        with pytest.raises(IntegrityError):
            db_session.commit()

class TestChatModel:
    """Test Chat model functionality."""
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_chat_creation(self, db_session, sample_tenant_data):
        """Test basic chat creation."""
        # Create tenant and user first
        tenant = Tenant(**sample_tenant_data)
        db_session.add(tenant)
        db_session.commit()
        db_session.refresh(tenant)
        
        user = TenantUser(
            name="Test User",
            role="admin",
            email="<EMAIL>",
            tenant_id=tenant.id,
            hashed_password="hashed_password_here"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        # Create chat
        chat = Chat(
            title="Test Chat",
            tenant_id=tenant.id,
            user_id=user.id
        )
        
        db_session.add(chat)
        db_session.commit()
        db_session.refresh(chat)
        
        assert chat.id is not None
        assert chat.title == "Test Chat"
        assert chat.tenant_id == tenant.id
        assert chat.user_id == user.id
        assert chat.status == "active"
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_chat_relationships(self, db_session, sample_tenant_data):
        """Test chat relationships with tenant and user."""
        # Create tenant and user
        tenant = Tenant(**sample_tenant_data)
        db_session.add(tenant)
        db_session.commit()
        db_session.refresh(tenant)
        
        user = TenantUser(
            name="Test User",
            role="admin",
            email="<EMAIL>",
            tenant_id=tenant.id,
            hashed_password="hashed_password_here"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        # Create chat
        chat = Chat(
            title="Test Chat",
            tenant_id=tenant.id,
            user_id=user.id
        )
        
        db_session.add(chat)
        db_session.commit()
        db_session.refresh(chat)
        
        # Test relationships
        assert chat.tenant is not None
        assert chat.tenant.id == tenant.id
        assert chat.user is not None
        assert chat.user.id == user.id

class TestMessageModel:
    """Test Message model functionality."""
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_message_creation(self, db_session, sample_tenant_data):
        """Test basic message creation."""
        # Create tenant, user, and chat first
        tenant = Tenant(**sample_tenant_data)
        db_session.add(tenant)
        db_session.commit()
        db_session.refresh(tenant)
        
        user = TenantUser(
            name="Test User",
            role="admin",
            email="<EMAIL>",
            tenant_id=tenant.id,
            hashed_password="hashed_password_here"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        chat = Chat(
            title="Test Chat",
            tenant_id=tenant.id,
            user_id=user.id
        )
        db_session.add(chat)
        db_session.commit()
        db_session.refresh(chat)
        
        # Create message
        message = Message(
            content="Test message content",
            sender="user",
            chat_id=chat.id,
            sql_query="SELECT * FROM users;",
            query_result="[{'id': 1, 'name': 'Test'}]"
        )
        
        db_session.add(message)
        db_session.commit()
        db_session.refresh(message)
        
        assert message.id is not None
        assert message.content == "Test message content"
        assert message.sender == "user"
        assert message.chat_id == chat.id
        assert message.sql_query == "SELECT * FROM users;"
        assert message.query_result == "[{'id': 1, 'name': 'Test'}]"
    
    @pytest.mark.unit
    @pytest.mark.database
    def test_message_chat_relationship(self, db_session, sample_tenant_data):
        """Test message-chat relationship."""
        # Create full hierarchy
        tenant = Tenant(**sample_tenant_data)
        db_session.add(tenant)
        db_session.commit()
        db_session.refresh(tenant)
        
        user = TenantUser(
            name="Test User",
            role="admin",
            email="<EMAIL>",
            tenant_id=tenant.id,
            hashed_password="hashed_password_here"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        chat = Chat(
            title="Test Chat",
            tenant_id=tenant.id,
            user_id=user.id
        )
        db_session.add(chat)
        db_session.commit()
        db_session.refresh(chat)
        
        message = Message(
            content="Test message",
            sender="user",
            chat_id=chat.id
        )
        db_session.add(message)
        db_session.commit()
        db_session.refresh(message)
        
        # Test relationship
        assert message.chat is not None
        assert message.chat.id == chat.id
        assert message.chat.title == "Test Chat"
