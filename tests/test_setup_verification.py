"""
Setup verification tests.

These tests verify that the testing infrastructure is working correctly.
"""

import pytest
import os
from fastapi.testclient import TestClient

class TestSetupVerification:
    """Verify that testing setup is working correctly."""
    
    @pytest.mark.unit
    def test_environment_variables(self):
        """Test that test environment variables are set correctly."""
        assert os.getenv("APP_ENV") == "test"
        assert os.getenv("JWT_SECRET_KEY") == "test-jwt-secret-key-for-testing-only"
        assert os.getenv("LOG_LEVEL") == "WARNING"
        assert os.getenv("ENABLE_RATE_LIMITING") == "false"
    
    @pytest.mark.unit
    def test_pytest_markers(self):
        """Test that pytest markers are working."""
        # This test itself uses the @pytest.mark.unit marker
        # If markers are working, this test will run
        assert True
    
    @pytest.mark.unit
    def test_basic_imports(self):
        """Test that basic imports are working."""
        # Test that we can import main modules
        try:
            import main
            import auth
            import models
            import database
            assert True
        except ImportError as e:
            pytest.fail(f"Import failed: {e}")
    
    @pytest.mark.integration
    def test_test_client_creation(self, client):
        """Test that FastAPI test client can be created."""
        assert isinstance(client, TestClient)
    
    @pytest.mark.integration
    def test_basic_health_endpoint(self, client):
        """Test that basic health endpoint works in test environment."""
        response = client.get("/")
        
        # Should get a response (might be 200 or error depending on setup)
        assert response.status_code in [200, 500, 503]
        
        # Should get JSON response
        try:
            data = response.json()
            assert isinstance(data, dict)
        except:
            # If not JSON, that's also acceptable for this basic test
            pass
    
    @pytest.mark.database
    def test_database_session_creation(self, db_session):
        """Test that database session can be created."""
        assert db_session is not None
        
        # Test basic database operation
        try:
            # Simple query to test connection
            result = db_session.execute("SELECT 1 as test")
            row = result.fetchone()
            assert row[0] == 1
        except Exception as e:
            # Database might not be available in test environment
            pytest.skip(f"Database not available: {e}")
    
    @pytest.mark.auth
    def test_auth_fixtures(self, test_user_data, test_admin_data):
        """Test that authentication fixtures are working."""
        assert "email" in test_user_data
        assert "password" in test_user_data
        assert "full_name" in test_user_data
        
        assert "email" in test_admin_data
        assert "password" in test_admin_data
        assert "full_name" in test_admin_data
        
        # Verify different emails
        assert test_user_data["email"] != test_admin_data["email"]
    
    @pytest.mark.unit
    def test_mock_fixtures(self, mock_openai, mock_mysql_manager):
        """Test that mock fixtures are working."""
        # Test OpenAI mock
        assert mock_openai is not None
        
        # Test MySQL manager mock
        assert mock_mysql_manager is not None
        
        # Test that mock returns expected data
        result = mock_mysql_manager.test_connection()
        assert result["status"] == "success"
        assert "mysql_version" in result
    
    @pytest.mark.unit
    def test_sample_data_fixtures(self, sample_tenant_data, sample_message_data):
        """Test that sample data fixtures are working."""
        # Test tenant data
        assert "employer_name" in sample_tenant_data
        assert "email" in sample_tenant_data
        
        # Test message data
        assert "message" in sample_message_data
        assert "selected_tables" in sample_message_data
    
    @pytest.mark.unit
    def test_password_manager_basic(self):
        """Test basic password manager functionality."""
        from auth import PasswordManager
        
        password = "TestPassword123!"
        hashed = PasswordManager.hash_password(password)
        
        assert hashed != password
        assert PasswordManager.verify_password(password, hashed) is True
        assert PasswordManager.verify_password("wrong", hashed) is False
    
    @pytest.mark.unit
    def test_jwt_manager_basic(self):
        """Test basic JWT manager functionality."""
        from auth import JWTManager
        
        data = {"sub": "test", "email": "<EMAIL>"}
        token = JWTManager.create_access_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 50
        
        # Verify token
        payload = JWTManager.verify_token(token, "access")
        assert payload["sub"] == "test"
        assert payload["email"] == "<EMAIL>"

class TestTestingInfrastructure:
    """Test the testing infrastructure itself."""
    
    @pytest.mark.unit
    def test_pytest_configuration(self):
        """Test that pytest is configured correctly."""
        # Test that we're in test environment
        assert os.getenv("APP_ENV") == "test"
        
        # Test that test database URL is set
        test_db_url = os.getenv("TEST_DATABASE_URL")
        assert test_db_url is not None
        assert "saas_test_db" in test_db_url
    
    @pytest.mark.unit
    def test_test_markers_available(self):
        """Test that all expected test markers are available."""
        # This test verifies that our custom markers are properly configured
        # The markers are defined in conftest.py and pytest.ini
        
        expected_markers = [
            "unit", "integration", "api", "database", 
            "auth", "ai", "slow", "security", "performance"
        ]
        
        # If this test runs without warnings about unknown markers,
        # then our markers are properly configured
        assert True
    
    @pytest.mark.unit
    def test_fixture_isolation(self, db_session):
        """Test that fixtures provide proper isolation."""
        # This test verifies that each test gets a fresh database session
        # and that changes don't persist between tests
        
        if db_session is not None:
            try:
                # Try to create a simple table for testing
                db_session.execute("""
                    CREATE TEMPORARY TABLE test_isolation (
                        id INT PRIMARY KEY,
                        value VARCHAR(50)
                    )
                """)
                
                # Insert test data
                db_session.execute(
                    "INSERT INTO test_isolation (id, value) VALUES (1, 'test')"
                )
                db_session.commit()
                
                # Verify data exists
                result = db_session.execute("SELECT value FROM test_isolation WHERE id = 1")
                row = result.fetchone()
                assert row[0] == "test"
                
            except Exception as e:
                # Database operations might fail in test environment
                pytest.skip(f"Database operations not available: {e}")
    
    @pytest.mark.integration
    def test_api_client_isolation(self, client):
        """Test that API client provides proper isolation."""
        # Test that each test gets a fresh API client
        assert isinstance(client, TestClient)
        
        # Test basic endpoint
        response = client.get("/")
        assert response.status_code in [200, 404, 500, 503]
    
    @pytest.mark.unit
    def test_mock_isolation(self, mock_openai):
        """Test that mocks are properly isolated."""
        # Test that mocks are fresh for each test
        assert mock_openai is not None
        
        # Verify mock behavior
        mock_client = mock_openai.return_value
        assert hasattr(mock_client, 'chat')

class TestCoverageVerification:
    """Tests to verify coverage reporting is working."""
    
    @pytest.mark.unit
    def test_covered_function(self):
        """Test a function to verify coverage tracking."""
        def simple_function(x):
            if x > 0:
                return "positive"
            elif x < 0:
                return "negative"
            else:
                return "zero"
        
        # Test all branches to ensure good coverage
        assert simple_function(1) == "positive"
        assert simple_function(-1) == "negative"
        assert simple_function(0) == "zero"
    
    @pytest.mark.unit
    def test_uncovered_function(self):
        """Test with intentionally uncovered code."""
        def function_with_uncovered_branch(x):
            if x > 0:
                return "positive"
            else:
                # This branch won't be tested, showing uncovered code
                return "not positive"  # pragma: no cover
        
        # Only test one branch
        assert function_with_uncovered_branch(1) == "positive"
        # The else branch remains uncovered for demonstration
