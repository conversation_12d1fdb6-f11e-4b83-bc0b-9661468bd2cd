"""
Integration tests for database operations.

Tests CRUD operations, MySQL manager, and database health checks.
"""

import pytest
from unittest.mock import patch, <PERSON><PERSON>

class TestDatabaseHealth:
    """Test database health and connectivity."""
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_mysql_health_endpoint(self, client):
        """Test MySQL health check endpoint."""
        response = client.get("/database/mysql/health")
        
        # This might fail if MySQL is not running, so we'll check both cases
        if response.status_code == 200:
            data = response.json()
            assert "status" in data
            assert "timestamp" in data
            if data["status"] == "success":
                assert "mysql_version" in data
        else:
            # If MySQL is not available, should return error status
            assert response.status_code == 503
            data = response.json()
            assert data["status"] == "error"
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_detailed_health_endpoint(self, client):
        """Test detailed health check endpoint."""
        response = client.get("/health")
        
        assert response.status_code in [200, 503]  # Depends on MySQL availability
        data = response.json()
        
        assert "status" in data
        assert "timestamp" in data
        assert "environment" in data
        assert "version" in data
        
        if "database" in data:
            assert "status" in data["database"]
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_readiness_probe(self, client):
        """Test Kubernetes readiness probe."""
        response = client.get("/health/ready")
        
        assert response.status_code in [200, 503]
        data = response.json()
        
        assert "status" in data
        assert "timestamp" in data
        assert data["status"] in ["ready", "not_ready"]
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_liveness_probe(self, client):
        """Test Kubernetes liveness probe."""
        response = client.get("/health/live")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "alive"
        assert "timestamp" in data

class TestDatabaseTables:
    """Test database table operations."""
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_get_database_tables(self, client):
        """Test getting database tables."""
        response = client.get("/database/tables")
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        # Should have some tables even if empty database
        # The exact tables depend on the database state
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_get_table_schema(self, client):
        """Test getting table schema."""
        # First get available tables
        tables_response = client.get("/database/tables")
        assert tables_response.status_code == 200
        
        tables = tables_response.json()
        
        if tables:
            # Test with first available table
            table_name = tables[0]["name"] if isinstance(tables[0], dict) else tables[0]
            
            response = client.get(f"/database/tables/{table_name}/schema")
            
            if response.status_code == 200:
                data = response.json()
                assert isinstance(data, dict)
                assert "columns" in data or "schema" in data
            else:
                # Some tables might not be accessible
                assert response.status_code in [404, 500]

class TestDatabaseQuery:
    """Test database query operations."""
    
    @pytest.mark.integration
    @pytest.mark.database
    @pytest.mark.ai
    def test_database_query_endpoint(self, client, mock_openai):
        """Test database query endpoint with mocked AI."""
        query_data = {
            "query": "Show me all users",
            "selected_tables": []
        }
        
        response = client.post("/database/query", json=query_data)
        
        # Response depends on AI integration and database state
        assert response.status_code in [200, 400, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "sql_query" in data or "response" in data
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_database_query_invalid_input(self, client):
        """Test database query with invalid input."""
        invalid_data = {
            "query": "",  # Empty query
            "selected_tables": []
        }
        
        response = client.post("/database/query", json=invalid_data)
        
        assert response.status_code in [400, 422]
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_database_query_missing_fields(self, client):
        """Test database query with missing fields."""
        incomplete_data = {
            "query": "Show me users"
            # Missing selected_tables
        }
        
        response = client.post("/database/query", json=incomplete_data)
        
        assert response.status_code == 422  # Validation error

class TestMySQLManagerIntegration:
    """Test MySQL manager integration with admin endpoints."""
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_mysql_status_endpoint_no_auth(self, client):
        """Test MySQL status endpoint without authentication."""
        response = client.get("/database/mysql/status")
        
        assert response.status_code == 403  # Should require authentication
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_mysql_status_endpoint_user_auth(self, client, auth_headers):
        """Test MySQL status endpoint with regular user authentication."""
        response = client.get("/database/mysql/status", headers=auth_headers)
        
        assert response.status_code == 403  # Should require admin privileges
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_mysql_status_endpoint_admin_auth(self, client, admin_headers):
        """Test MySQL status endpoint with admin authentication."""
        response = client.get("/database/mysql/status", headers=admin_headers)
        
        # Depends on whether admin user is properly created and MySQL is available
        assert response.status_code in [200, 403, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "connection" in data
            assert "database_info" in data
            assert "timestamp" in data
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_mysql_optimize_endpoint(self, client, admin_headers):
        """Test MySQL optimization endpoint."""
        response = client.post("/database/mysql/optimize", headers=admin_headers)
        
        # Depends on admin auth and MySQL availability
        assert response.status_code in [200, 403, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "status" in data
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_mysql_backup_endpoint(self, client, admin_headers):
        """Test MySQL backup endpoint."""
        backup_data = {"backup_name": "test_backup.sql"}
        
        response = client.post(
            "/database/mysql/backup",
            headers=admin_headers,
            params=backup_data
        )
        
        # Depends on admin auth, MySQL availability, and mysqldump
        assert response.status_code in [200, 403, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "status" in data

class TestCRUDOperations:
    """Test CRUD operations through the API."""
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_tenant_crud_operations(self, client, auth_headers, sample_tenant_data):
        """Test tenant CRUD operations."""
        # This test assumes there are tenant management endpoints
        # The actual endpoints depend on your API design
        
        # Create tenant (if endpoint exists)
        create_response = client.post(
            "/tenants",
            json=sample_tenant_data,
            headers=auth_headers
        )
        
        # The response depends on whether tenant endpoints are implemented
        if create_response.status_code == 404:
            pytest.skip("Tenant endpoints not implemented")
        
        if create_response.status_code == 201:
            tenant_data = create_response.json()
            tenant_id = tenant_data["id"]
            
            # Read tenant
            read_response = client.get(f"/tenants/{tenant_id}", headers=auth_headers)
            assert read_response.status_code == 200
            
            # Update tenant
            update_data = {**sample_tenant_data, "employer_name": "Updated Company"}
            update_response = client.put(
                f"/tenants/{tenant_id}",
                json=update_data,
                headers=auth_headers
            )
            assert update_response.status_code in [200, 204]
            
            # Delete tenant
            delete_response = client.delete(f"/tenants/{tenant_id}", headers=auth_headers)
            assert delete_response.status_code in [200, 204]

class TestDatabaseTransactions:
    """Test database transaction handling."""
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_transaction_rollback_on_error(self, db_session, sample_tenant_data):
        """Test that database transactions rollback on errors."""
        from models import Tenant
        from sqlalchemy.exc import IntegrityError
        
        # Create a valid tenant
        tenant1 = Tenant(**sample_tenant_data)
        db_session.add(tenant1)
        db_session.commit()
        
        # Try to create duplicate tenant (should fail)
        tenant2 = Tenant(**sample_tenant_data)  # Same email
        db_session.add(tenant2)
        
        with pytest.raises(IntegrityError):
            db_session.commit()
        
        # Session should be rolled back
        db_session.rollback()
        
        # Original tenant should still exist
        existing_tenant = db_session.query(Tenant).filter_by(
            email=sample_tenant_data["email"]
        ).first()
        assert existing_tenant is not None
        assert existing_tenant.employer_name == sample_tenant_data["employer_name"]
    
    @pytest.mark.integration
    @pytest.mark.database
    def test_concurrent_operations(self, test_session_factory, sample_tenant_data):
        """Test concurrent database operations."""
        from models import Tenant
        
        # Create two separate sessions
        session1 = test_session_factory()
        session2 = test_session_factory()
        
        try:
            # Create tenant in session1
            tenant1 = Tenant(**sample_tenant_data)
            session1.add(tenant1)
            session1.commit()
            
            # Read from session2
            tenant2 = session2.query(Tenant).filter_by(
                email=sample_tenant_data["email"]
            ).first()
            
            assert tenant2 is not None
            assert tenant2.employer_name == sample_tenant_data["employer_name"]
            
        finally:
            session1.close()
            session2.close()

class TestDatabasePerformance:
    """Test database performance characteristics."""
    
    @pytest.mark.integration
    @pytest.mark.database
    @pytest.mark.performance
    @pytest.mark.slow
    def test_bulk_insert_performance(self, db_session):
        """Test bulk insert performance."""
        from models import Tenant
        import time
        
        # Create multiple tenants
        tenants = []
        for i in range(100):
            tenant = Tenant(
                employer_name=f"Company {i}",
                email=f"company{i}@example.com"
            )
            tenants.append(tenant)
        
        # Measure bulk insert time
        start_time = time.time()
        db_session.add_all(tenants)
        db_session.commit()
        end_time = time.time()
        
        insert_time = end_time - start_time
        
        # Should complete within reasonable time (adjust threshold as needed)
        assert insert_time < 5.0  # 5 seconds for 100 records
        
        # Verify all records were inserted
        count = db_session.query(Tenant).count()
        assert count >= 100
    
    @pytest.mark.integration
    @pytest.mark.database
    @pytest.mark.performance
    def test_query_performance(self, db_session, sample_tenant_data):
        """Test query performance."""
        from models import Tenant
        import time
        
        # Create some test data
        for i in range(50):
            tenant = Tenant(
                employer_name=f"Company {i}",
                email=f"company{i}@example.com"
            )
            db_session.add(tenant)
        db_session.commit()
        
        # Measure query time
        start_time = time.time()
        tenants = db_session.query(Tenant).all()
        end_time = time.time()
        
        query_time = end_time - start_time
        
        # Should complete quickly
        assert query_time < 1.0  # 1 second for simple query
        assert len(tenants) >= 50
