#!/usr/bin/env python3
"""
Test script to verify backend functionality
"""

import requests
import json
import time

def test_backend():
    """Test backend endpoints"""
    base_url = "http://localhost:8000"
    
    print("🔍 Testing Backend Endpoints...")
    
    # Test 1: Health check
    print("\n1. Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   Response: {response.json()}")
            print("   ✅ Health check passed")
        else:
            print(f"   ❌ Health check failed: {response.text}")
    except Exception as e:
        print(f"   ❌ Health check error: {e}")
    
    # Test 2: Root endpoint
    print("\n2. Testing root endpoint...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   Response: {response.json()}")
            print("   ✅ Root endpoint passed")
        else:
            print(f"   ❌ Root endpoint failed: {response.text}")
    except Exception as e:
        print(f"   ❌ Root endpoint error: {e}")
    
    # Test 3: Docs endpoint
    print("\n3. Testing docs endpoint...")
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Docs endpoint accessible")
        else:
            print(f"   ❌ Docs endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Docs endpoint error: {e}")
    
    # Test 4: OpenAPI schema
    print("\n4. Testing OpenAPI schema...")
    try:
        response = requests.get(f"{base_url}/openapi.json", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            schema = response.json()
            print(f"   Title: {schema.get('info', {}).get('title', 'Unknown')}")
            print("   ✅ OpenAPI schema accessible")
        else:
            print(f"   ❌ OpenAPI schema failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ OpenAPI schema error: {e}")
    
    # Test 5: Admin endpoint (should be accessible in dev)
    print("\n5. Testing admin endpoint...")
    try:
        response = requests.get(f"{base_url}/admin", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code in [200, 401, 422]:  # 401/422 is OK, means endpoint exists
            print("   ✅ Admin endpoint accessible")
        elif response.status_code == 403:
            print("   ⚠️  Admin endpoint blocked (IP whitelist active)")
        else:
            print(f"   ❌ Admin endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Admin endpoint error: {e}")

if __name__ == "__main__":
    test_backend()
