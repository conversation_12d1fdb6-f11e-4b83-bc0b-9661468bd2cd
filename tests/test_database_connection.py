#!/usr/bin/env python3
"""
Test suite for Database Connection Manager.
Tests the logical deduplication improvements in query_agent.py.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from typing import List, Dict, Any

# Import query agent components
from query_agent import DatabaseConnectionManager, run_nl_to_sql, get_database_tables, get_table_schema

class TestDatabaseConnectionManager:
    """Test Database Connection Manager functionality."""
    
    def test_mock_tables_generation(self):
        """Test that mock tables are generated consistently."""
        mock_tables = DatabaseConnectionManager.get_mock_tables()
        
        # Check structure
        assert isinstance(mock_tables, list)
        assert len(mock_tables) >= 3  # Should have users, orders, products at minimum
        
        # Check each table has required structure
        for table in mock_tables:
            assert isinstance(table, dict)
            assert 'name' in table
            assert 'columns' in table
            assert isinstance(table['columns'], list)
            
            # Check columns have required structure
            for column in table['columns']:
                assert isinstance(column, dict)
                assert 'name' in column
                assert 'type' in column
        
        # Check specific expected tables
        table_names = [table['name'] for table in mock_tables]
        assert 'users' in table_names
        assert 'orders' in table_names
        assert 'products' in table_names
        
        print("✅ Mock tables generation test passed")
    
    def test_mock_schema_generation(self):
        """Test that mock schemas are generated consistently."""
        # Test known table
        users_schema = DatabaseConnectionManager.get_mock_schema('users')
        
        assert isinstance(users_schema, dict)
        assert 'table_name' in users_schema
        assert 'columns' in users_schema
        assert users_schema['table_name'] == 'users'
        assert isinstance(users_schema['columns'], list)
        
        # Check columns structure
        for column in users_schema['columns']:
            assert isinstance(column, dict)
            assert 'name' in column
            assert 'type' in column
        
        # Test unknown table (should get default)
        unknown_schema = DatabaseConnectionManager.get_mock_schema('unknown_table')
        assert unknown_schema['table_name'] == 'unknown_table'
        assert len(unknown_schema['columns']) >= 2  # Should have id and data at minimum
        
        print("✅ Mock schema generation test passed")
    
    def test_with_database_connection_fallback(self):
        """Test that database connection manager handles fallbacks correctly."""
        # Test with invalid database URI (should trigger fallback)
        invalid_uri = "invalid://database/uri"
        
        def test_operation(db):
            # This should never be called due to invalid URI
            return "database_result"
        
        result = DatabaseConnectionManager.with_database_connection(
            client_db_uri=invalid_uri,
            operation=test_operation,
            fallback_data="fallback_result"
        )
        
        # Should return fallback data
        assert result == "fallback_result"
        
        print("✅ Database connection fallback test passed")
    
    def test_run_nl_to_sql_with_fallback(self):
        """Test that run_nl_to_sql uses connection manager correctly."""
        # Test with invalid database URI (should use mock SQL generation)
        invalid_uri = "invalid://database/uri"
        query = "show me all users"
        
        result = run_nl_to_sql(invalid_uri, query)
        
        # Should return some SQL (mock generation)
        assert isinstance(result, str)
        assert len(result) > 0
        assert 'SELECT' in result.upper()  # Should be SQL
        
        print("✅ run_nl_to_sql fallback test passed")
    
    def test_get_database_tables_with_fallback(self):
        """Test that get_database_tables uses connection manager correctly."""
        # Test with invalid database URI (should use mock tables)
        invalid_uri = "invalid://database/uri"
        
        result = get_database_tables(invalid_uri)
        
        # Should return mock tables
        assert isinstance(result, list)
        assert len(result) >= 3
        
        # Should match mock tables structure
        mock_tables = DatabaseConnectionManager.get_mock_tables()
        assert len(result) == len(mock_tables)
        
        # Check structure matches
        for i, table in enumerate(result):
            assert table['name'] == mock_tables[i]['name']
            assert len(table['columns']) == len(mock_tables[i]['columns'])
        
        print("✅ get_database_tables fallback test passed")
    
    def test_get_table_schema_with_fallback(self):
        """Test that get_table_schema uses connection manager correctly."""
        # Test with invalid database URI (should use mock schema)
        invalid_uri = "invalid://database/uri"
        table_name = "users"
        
        result = get_table_schema(invalid_uri, table_name)
        
        # Should return mock schema
        assert isinstance(result, dict)
        assert 'table_name' in result
        assert result['table_name'] == table_name
        
        # Should match mock schema structure
        mock_schema = DatabaseConnectionManager.get_mock_schema(table_name)
        if 'columns' in mock_schema:
            assert 'columns' in result or 'schema' in result
        
        print("✅ get_table_schema fallback test passed")
    
    def test_consistent_mock_data_across_functions(self):
        """Test that mock data is consistent across all functions."""
        # Get mock data from different sources
        mock_tables_direct = DatabaseConnectionManager.get_mock_tables()
        mock_tables_from_function = get_database_tables("invalid://uri")
        
        # Should be identical
        assert len(mock_tables_direct) == len(mock_tables_from_function)
        
        for i, (direct, from_func) in enumerate(zip(mock_tables_direct, mock_tables_from_function)):
            assert direct['name'] == from_func['name']
            assert len(direct['columns']) == len(from_func['columns'])
        
        # Test schema consistency
        for table in mock_tables_direct:
            table_name = table['name']
            schema_direct = DatabaseConnectionManager.get_mock_schema(table_name)
            schema_from_function = get_table_schema("invalid://uri", table_name)
            
            assert schema_direct['table_name'] == schema_from_function['table_name']
        
        print("✅ Consistent mock data test passed")
    
    def test_connection_manager_eliminates_duplication(self):
        """Test that connection manager eliminates code duplication."""
        # Test that all functions use the same connection logic
        invalid_uri = "invalid://database/uri"
        
        # All these should handle the invalid URI gracefully (no exceptions)
        try:
            sql_result = run_nl_to_sql(invalid_uri, "test query")
            tables_result = get_database_tables(invalid_uri)
            schema_result = get_table_schema(invalid_uri, "users")
            
            # All should return valid results (fallback data)
            assert sql_result is not None
            assert tables_result is not None
            assert schema_result is not None
            
            # All should be using the same error handling logic
            assert isinstance(sql_result, str)
            assert isinstance(tables_result, list)
            assert isinstance(schema_result, dict)
            
        except Exception as e:
            # Should not raise exceptions due to centralized error handling
            assert False, f"Connection manager should handle errors gracefully: {e}"
        
        print("✅ Connection manager duplication elimination test passed")

def run_database_connection_tests():
    """Run all database connection tests."""
    print("🧪 Starting Database Connection Manager Tests...")
    
    test_instance = TestDatabaseConnectionManager()
    
    try:
        test_instance.test_mock_tables_generation()
        test_instance.test_mock_schema_generation()
        test_instance.test_with_database_connection_fallback()
        test_instance.test_run_nl_to_sql_with_fallback()
        test_instance.test_get_database_tables_with_fallback()
        test_instance.test_get_table_schema_with_fallback()
        test_instance.test_consistent_mock_data_across_functions()
        test_instance.test_connection_manager_eliminates_duplication()
        
        print("🎉 All Database Connection Manager Tests Passed!")
        return True
        
    except Exception as e:
        print(f"❌ Database connection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_database_connection_tests()
    sys.exit(0 if success else 1)
