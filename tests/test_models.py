#!/usr/bin/env python3
"""
Test suite for model mixins and base classes.
Tests the logical deduplication improvements in models.py.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime

# Import models and database components
from models import Base, Tenant, TenantUser, AdminUser, TimestampMixin, StatusMixin, EmailMixin, BaseModel
from database import get_db_session

# Test database setup
TEST_DATABASE_URL = "sqlite:///./test_models.db"
test_engine = create_engine(TEST_DATABASE_URL, connect_args={"check_same_thread": False})
TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)

def setup_test_db():
    """Create test database tables."""
    Base.metadata.create_all(bind=test_engine)

def teardown_test_db():
    """Drop test database tables."""
    Base.metadata.drop_all(bind=test_engine)

def get_test_db():
    """Get test database session."""
    db = TestSessionLocal()
    try:
        return db
    finally:
        pass  # Don't close here, let test handle it

class TestModelMixins:
    """Test model mixins functionality."""
    
    def setup_method(self):
        """Setup for each test method."""
        setup_test_db()
        self.db = get_test_db()
    
    def teardown_method(self):
        """Cleanup after each test method."""
        self.db.close()
        teardown_test_db()
    
    def test_timestamp_mixin_inheritance(self):
        """Test that models inherit timestamp fields from TimestampMixin."""
        # Test Tenant model
        tenant = Tenant(employer_name="Test Company", email="<EMAIL>")
        self.db.add(tenant)
        self.db.commit()
        self.db.refresh(tenant)
        
        # Check timestamp fields exist and are populated
        assert hasattr(tenant, 'created_at')
        assert hasattr(tenant, 'updated_at')
        assert tenant.created_at is not None
        assert tenant.updated_at is not None
        assert isinstance(tenant.created_at, datetime)
        assert isinstance(tenant.updated_at, datetime)
        
        print("✅ TimestampMixin inheritance test passed")
    
    def test_status_mixin_inheritance(self):
        """Test that models inherit status field from StatusMixin."""
        # Test Tenant model
        tenant = Tenant(employer_name="Test Company", email="<EMAIL>")
        self.db.add(tenant)
        self.db.commit()
        self.db.refresh(tenant)
        
        # Check status field exists and has default value
        assert hasattr(tenant, 'status')
        assert tenant.status == 'active'  # Default value
        
        # Test TenantUser model
        tenant_user = TenantUser(
            name="Test User",
            role="admin", 
            email="<EMAIL>",
            tenant_id=tenant.id,
            hashed_password="hashed_password"
        )
        self.db.add(tenant_user)
        self.db.commit()
        self.db.refresh(tenant_user)
        
        assert hasattr(tenant_user, 'status')
        assert tenant_user.status == 'active'
        
        print("✅ StatusMixin inheritance test passed")
    
    def test_email_mixin_inheritance(self):
        """Test that models inherit email field from EmailMixin."""
        # Test Tenant model
        tenant = Tenant(employer_name="Test Company", email="<EMAIL>")
        self.db.add(tenant)
        self.db.commit()
        self.db.refresh(tenant)
        
        # Check email field exists and is indexed
        assert hasattr(tenant, 'email')
        assert tenant.email == "<EMAIL>"
        
        # Test TenantUser model  
        tenant_user = TenantUser(
            name="Test User",
            role="admin",
            email="<EMAIL>", 
            tenant_id=tenant.id,
            hashed_password="hashed_password"
        )
        self.db.add(tenant_user)
        self.db.commit()
        self.db.refresh(tenant_user)
        
        assert hasattr(tenant_user, 'email')
        assert tenant_user.email == "<EMAIL>"
        
        print("✅ EmailMixin inheritance test passed")
    
    def test_base_model_inheritance(self):
        """Test that models inherit common functionality from BaseModel."""
        # Test Tenant model
        tenant = Tenant(employer_name="Test Company", email="<EMAIL>")
        self.db.add(tenant)
        self.db.commit()
        self.db.refresh(tenant)
        
        # Check BaseModel methods
        assert hasattr(tenant, 'to_dict')
        assert hasattr(tenant, '__repr__')
        assert hasattr(tenant, 'id')
        assert isinstance(tenant.id, int)
        
        # Test to_dict method
        tenant_dict = tenant.to_dict()
        assert isinstance(tenant_dict, dict)
        assert 'id' in tenant_dict
        assert 'employer_name' in tenant_dict
        assert 'email' in tenant_dict
        assert 'status' in tenant_dict
        assert 'created_at' in tenant_dict
        assert 'updated_at' in tenant_dict
        
        # Test __repr__ method
        repr_str = repr(tenant)
        assert 'Tenant' in repr_str
        assert str(tenant.id) in repr_str
        
        print("✅ BaseModel inheritance test passed")
    
    def test_multiple_mixin_inheritance(self):
        """Test that models can inherit from multiple mixins correctly."""
        # Test Tenant (inherits from BaseModel, StatusMixin, EmailMixin)
        tenant = Tenant(employer_name="Test Company", email="<EMAIL>", phone="************")
        self.db.add(tenant)
        self.db.commit()
        self.db.refresh(tenant)
        
        # Check all inherited fields
        assert hasattr(tenant, 'id')  # From BaseModel
        assert hasattr(tenant, 'created_at')  # From TimestampMixin (via BaseModel)
        assert hasattr(tenant, 'updated_at')  # From TimestampMixin (via BaseModel)
        assert hasattr(tenant, 'status')  # From StatusMixin
        assert hasattr(tenant, 'email')  # From EmailMixin
        
        # Check model-specific fields
        assert hasattr(tenant, 'employer_name')
        assert hasattr(tenant, 'phone')
        
        # Check values
        assert tenant.employer_name == "Test Company"
        assert tenant.email == "<EMAIL>"
        assert tenant.phone == "************"
        assert tenant.status == 'active'
        assert tenant.id is not None
        assert tenant.created_at is not None
        assert tenant.updated_at is not None
        
        print("✅ Multiple mixin inheritance test passed")

def run_model_tests():
    """Run all model tests."""
    print("🧪 Starting Model Mixin Tests...")
    
    test_instance = TestModelMixins()
    
    try:
        test_instance.setup_method()
        test_instance.test_timestamp_mixin_inheritance()
        test_instance.teardown_method()
        
        test_instance.setup_method()
        test_instance.test_status_mixin_inheritance()
        test_instance.teardown_method()
        
        test_instance.setup_method()
        test_instance.test_email_mixin_inheritance()
        test_instance.teardown_method()
        
        test_instance.setup_method()
        test_instance.test_base_model_inheritance()
        test_instance.teardown_method()
        
        test_instance.setup_method()
        test_instance.test_multiple_mixin_inheritance()
        test_instance.teardown_method()
        
        print("🎉 All Model Mixin Tests Passed!")
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_model_tests()
    sys.exit(0 if success else 1)
