#!/usr/bin/env python3
"""
Quick start script to run the backend without <PERSON><PERSON> for testing
"""

import subprocess
import sys
import os
import time

def install_minimal_requirements():
    """Install only the essential requirements for testing"""
    minimal_requirements = [
        "fastapi",
        "uvicorn[standard]",
        "python-dotenv",
        "sqlalchemy>=2.0.0",
        "pymysql",
        "cryptography",
        "passlib[bcrypt]",
        "python-jose[cryptography]",
        "python-multipart",
        "PyJWT",
        "bcrypt",
        "jinja2",
        "pydantic",
        "starlette"
    ]
    
    print("🔧 Installing minimal requirements...")
    for req in minimal_requirements:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", req])
            print(f"✅ Installed {req}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {req}: {e}")
            return False
    return True

def setup_environment():
    """Setup environment variables"""
    env_vars = {
        "APP_ENV": "development",
        "JWT_SECRET_KEY": "dev-jwt-secret-key-not-for-production",
        "DEV_DATABASE_URL": "sqlite:///./test.db",  # Use SQLite for quick testing
        "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY", ""),
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"🔧 Set {key}")

def create_minimal_main():
    """Create a minimal main.py for testing"""
    minimal_main = '''
import os
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(
    title="My SaaS Backend - Quick Test",
    description="Minimal version for testing",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def health_check():
    """Basic health check endpoint."""
    return {"status": "healthy", "message": "My SaaS Backend is running"}

@app.get("/health")
def detailed_health_check():
    """Detailed health check for monitoring systems."""
    return {
        "status": "healthy",
        "environment": os.getenv("APP_ENV", "development"),
        "version": "1.0.0",
        "database": "connected"
    }

@app.get("/test")
def test_endpoint():
    """Test endpoint to verify everything works"""
    return {
        "message": "Backend is working!",
        "endpoints": [
            "/",
            "/health", 
            "/test",
            "/docs",
            "/openapi.json"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
    
    with open("main_minimal.py", "w") as f:
        f.write(minimal_main)
    print("✅ Created minimal main.py")

def run_server():
    """Run the minimal server"""
    print("🚀 Starting minimal backend server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📖 API docs will be available at: http://localhost:8000/docs")
    print("🔍 Health check: http://localhost:8000/health")
    print("\n⏹️  Press Ctrl+C to stop the server\n")
    
    try:
        subprocess.run([sys.executable, "-m", "uvicorn", "main_minimal:app", "--host", "0.0.0.0", "--port", "8000", "--reload"])
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")

def main():
    """Main function"""
    print("🚀 Quick Start - My SaaS Backend")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("main.py"):
        print("❌ Please run this script from the My_SaaS_Backend directory")
        return
    
    # Install minimal requirements
    if not install_minimal_requirements():
        print("❌ Failed to install requirements")
        return
    
    # Setup environment
    setup_environment()
    
    # Create minimal main
    create_minimal_main()
    
    # Run server
    run_server()

if __name__ == "__main__":
    main()
