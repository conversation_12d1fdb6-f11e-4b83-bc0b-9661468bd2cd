#!/bin/bash

echo "🚀 Fixing All Dependencies for SaaS Backend"
echo "============================================"

# Function to install a package and check if it worked
install_and_check() {
    local package=$1
    local import_name=${2:-$1}
    
    echo "📦 Installing $package..."
    pip3 install "$package"
    
    echo "🧪 Testing $import_name import..."
    if python3 -c "import $import_name; print('✅ $import_name works')" 2>/dev/null; then
        echo "✅ $package installed successfully"
        return 0
    else
        echo "❌ $package installation failed"
        return 1
    fi
}

# Install core dependencies
echo "🔧 Installing core dependencies..."
install_and_check "fastapi" "fastapi"
install_and_check "uvicorn[standard]" "uvicorn"
install_and_check "python-dotenv" "dotenv"
install_and_check "sqlalchemy>=2.0.0" "sqlalchemy"
install_and_check "pymysql" "pymysql"
install_and_check "cryptography" "cryptography"

# Install authentication dependencies
echo ""
echo "🔐 Installing authentication dependencies..."
install_and_check "PyJWT>=2.8.0" "jwt"
install_and_check "passlib[bcrypt]" "passlib"
install_and_check "python-jose[cryptography]" "jose"
install_and_check "python-multipart" "multipart"
install_and_check "bcrypt" "bcrypt"

# Install security dependencies
echo ""
echo "🛡️ Installing security dependencies..."
install_and_check "bleach>=6.0.0" "bleach"
install_and_check "user-agents>=2.2.0" "user_agents"

# Install LangChain dependencies
echo ""
echo "🤖 Installing LangChain dependencies..."
install_and_check "langchain" "langchain"
install_and_check "langchain-community" "langchain_community"
install_and_check "langchain-openai" "langchain_openai"
install_and_check "openai" "openai"

# Install template and validation dependencies
echo ""
echo "🎨 Installing template and validation dependencies..."
install_and_check "jinja2" "jinja2"
install_and_check "pydantic" "pydantic"
install_and_check "starlette" "starlette"

# Test all critical imports
echo ""
echo "🧪 Testing all critical imports..."
python3 -c "
import sys
success = True

# Test core imports
try:
    import fastapi
    import uvicorn
    import sqlalchemy
    import pymysql
    print('✅ Core dependencies working')
except ImportError as e:
    print(f'❌ Core dependency failed: {e}')
    success = False

# Test auth imports
try:
    import jwt
    import passlib
    import bcrypt
    from jose import jwt as jose_jwt
    print('✅ Authentication dependencies working')
except ImportError as e:
    print(f'❌ Auth dependency failed: {e}')
    success = False

# Test security imports
try:
    import bleach
    print('✅ Security dependencies working')
except ImportError as e:
    print(f'❌ Security dependency failed: {e}')
    success = False

# Test template imports
try:
    import jinja2
    import pydantic
    import starlette
    print('✅ Template dependencies working')
except ImportError as e:
    print(f'❌ Template dependency failed: {e}')
    success = False

if success:
    print('\\n🎉 All dependencies are working!')
    sys.exit(0)
else:
    print('\\n❌ Some dependencies failed')
    sys.exit(1)
"

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 All dependencies fixed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Run your app: python3 main.py"
    echo "2. Or with uvicorn: uvicorn main:app --reload"
    echo "3. Or with Docker: docker-compose -f docker-compose.simple.yml up --build"
else
    echo ""
    echo "❌ Some dependencies still have issues."
    echo "Try running the Docker setup instead:"
    echo "docker-compose -f docker-compose.simple.yml up --build"
fi
