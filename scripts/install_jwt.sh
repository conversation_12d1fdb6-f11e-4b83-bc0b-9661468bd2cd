#!/bin/bash

echo "🔧 Installing JWT and authentication packages..."

# Install the required packages
pip3 install PyJWT>=2.8.0
pip3 install passlib[bcrypt]
pip3 install python-jose[cryptography]
pip3 install python-multipart
pip3 install bcrypt

echo "✅ Installation complete!"

# Test the installation
echo "🧪 Testing JWT import..."
python3 -c "
import jwt
print('✅ JWT import successful')

# Test basic functionality
test_payload = {'test': 'data'}
test_secret = 'test-secret'
token = jwt.encode(test_payload, test_secret, algorithm='HS256')
decoded = jwt.decode(token, test_secret, algorithms=['HS256'])
print('✅ JWT functionality test passed')
"

echo "🎉 JWT setup complete! You can now run your application."
