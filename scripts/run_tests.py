#!/usr/bin/env python3
"""
Test Runner for SaaS Backend

This script provides a convenient way to run different types of tests
with proper environment setup and reporting.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def setup_test_environment():
    """Set up test environment variables."""
    test_env = {
        "APP_ENV": "test",
        "TEST_DATABASE_URL": "mysql+pymysql://root:@localhost:3306/saas_test_db",
        "JWT_SECRET_KEY": "test-jwt-secret-key-for-testing-only",
        "OPENAI_API_KEY": "test-openai-key",
        "LOG_LEVEL": "WARNING",
        "ENABLE_RATE_LIMITING": "false",
        "SQL_ECHO": "false",
    }
    
    for key, value in test_env.items():
        os.environ[key] = value

def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False

def install_test_dependencies():
    """Install test dependencies."""
    cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]
    return run_command(cmd, "Installing test dependencies")

def run_unit_tests(verbose=False, coverage=False):
    """Run unit tests."""
    cmd = [sys.executable, "-m", "pytest", "tests/unit/", "-m", "unit"]
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend(["--cov=.", "--cov-report=term-missing"])
    
    return run_command(cmd, "Running unit tests")

def run_integration_tests(verbose=False):
    """Run integration tests."""
    cmd = [sys.executable, "-m", "pytest", "tests/integration/", "-m", "integration"]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, "Running integration tests")

def run_api_tests(verbose=False):
    """Run API tests."""
    cmd = [sys.executable, "-m", "pytest", "tests/api/", "-m", "api"]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, "Running API tests")

def run_auth_tests(verbose=False):
    """Run authentication tests."""
    cmd = [sys.executable, "-m", "pytest", "-m", "auth"]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, "Running authentication tests")

def run_database_tests(verbose=False):
    """Run database tests."""
    cmd = [sys.executable, "-m", "pytest", "-m", "database"]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, "Running database tests")

def run_ai_tests(verbose=False):
    """Run AI integration tests."""
    cmd = [sys.executable, "-m", "pytest", "-m", "ai"]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, "Running AI integration tests")

def run_all_tests(verbose=False, coverage=False):
    """Run all tests."""
    cmd = [sys.executable, "-m", "pytest", "tests/"]
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend(["--cov=.", "--cov-report=term-missing", "--cov-report=html"])
    
    return run_command(cmd, "Running all tests")

def run_fast_tests(verbose=False):
    """Run only fast tests (exclude slow tests)."""
    cmd = [sys.executable, "-m", "pytest", "tests/", "-m", "not slow"]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, "Running fast tests")

def run_security_tests(verbose=False):
    """Run security tests."""
    cmd = [sys.executable, "-m", "pytest", "-m", "security"]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, "Running security tests")

def run_performance_tests(verbose=False):
    """Run performance tests."""
    cmd = [sys.executable, "-m", "pytest", "-m", "performance"]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd, "Running performance tests")

def check_test_database():
    """Check if test database is accessible."""
    try:
        import pymysql
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            database='saas_test_db'
        )
        connection.close()
        print("✅ Test database is accessible")
        return True
    except Exception as e:
        print(f"❌ Test database is not accessible: {e}")
        print("💡 Make sure MySQL is running and test database exists")
        print("💡 Run: python setup_mysql_all_envs.py --env test")
        return False

def generate_test_report():
    """Generate comprehensive test report."""
    cmd = [
        sys.executable, "-m", "pytest", "tests/",
        "--cov=.", "--cov-report=html", "--cov-report=xml",
        "--junit-xml=test-results.xml",
        "--html=test-report.html", "--self-contained-html"
    ]
    
    return run_command(cmd, "Generating comprehensive test report")

def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="SaaS Backend Test Runner")
    parser.add_argument("--type", choices=[
        "unit", "integration", "api", "auth", "database", "ai", 
        "all", "fast", "security", "performance"
    ], default="fast", help="Type of tests to run")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--coverage", "-c", action="store_true", help="Generate coverage report")
    parser.add_argument("--install-deps", action="store_true", help="Install test dependencies")
    parser.add_argument("--check-db", action="store_true", help="Check test database connectivity")
    parser.add_argument("--report", action="store_true", help="Generate comprehensive test report")
    
    args = parser.parse_args()
    
    print("🧪 SaaS Backend Test Runner")
    print("=" * 50)
    
    # Set up test environment
    setup_test_environment()
    
    # Install dependencies if requested
    if args.install_deps:
        if not install_test_dependencies():
            sys.exit(1)
    
    # Check database connectivity if requested
    if args.check_db:
        if not check_test_database():
            sys.exit(1)
    
    # Generate report if requested
    if args.report:
        if not generate_test_report():
            sys.exit(1)
        return
    
    # Run tests based on type
    success = False
    
    if args.type == "unit":
        success = run_unit_tests(args.verbose, args.coverage)
    elif args.type == "integration":
        success = run_integration_tests(args.verbose)
    elif args.type == "api":
        success = run_api_tests(args.verbose)
    elif args.type == "auth":
        success = run_auth_tests(args.verbose)
    elif args.type == "database":
        success = run_database_tests(args.verbose)
    elif args.type == "ai":
        success = run_ai_tests(args.verbose)
    elif args.type == "all":
        success = run_all_tests(args.verbose, args.coverage)
    elif args.type == "fast":
        success = run_fast_tests(args.verbose)
    elif args.type == "security":
        success = run_security_tests(args.verbose)
    elif args.type == "performance":
        success = run_performance_tests(args.verbose)
    
    if success:
        print("\n🎉 Tests completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
