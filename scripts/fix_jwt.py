#!/usr/bin/env python3
"""
Quick fix for JWT import error.
This script installs the correct JWT package and tests the import.
"""

import subprocess
import sys
import os

def install_jwt_package():
    """Install PyJWT package specifically."""
    print("🔧 Installing PyJWT package...")
    
    try:
        # Install PyJWT specifically
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PyJWT>=2.8.0"])
        print("✅ PyJWT installed successfully")
        
        # Also ensure we have the other auth packages
        auth_packages = [
            "passlib[bcrypt]",
            "python-jose[cryptography]", 
            "python-multipart",
            "bcrypt"
        ]
        
        for package in auth_packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package} installed successfully")
            except subprocess.CalledProcessError as e:
                print(f"⚠️  Warning: Failed to install {package}: {e}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install PyJWT: {e}")
        return False

def test_jwt_import():
    """Test if JWT can be imported correctly."""
    print("\n🧪 Testing JWT import...")
    
    try:
        import jwt
        print("✅ JWT import successful")
        
        # Test basic JWT functionality
        test_payload = {"test": "data"}
        test_secret = "test-secret"
        
        # Encode
        token = jwt.encode(test_payload, test_secret, algorithm="HS256")
        print("✅ JWT encode successful")
        
        # Decode
        decoded = jwt.decode(token, test_secret, algorithms=["HS256"])
        print("✅ JWT decode successful")
        
        if decoded == test_payload:
            print("✅ JWT functionality test passed")
            return True
        else:
            print("❌ JWT functionality test failed")
            return False
            
    except ImportError as e:
        print(f"❌ JWT import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ JWT test failed: {e}")
        return False

def test_other_auth_imports():
    """Test other authentication-related imports."""
    print("\n🧪 Testing other auth imports...")
    
    imports_to_test = [
        ("passlib.context", "CryptContext"),
        ("bcrypt", None),
        ("jose", "jwt"),
    ]
    
    success = True
    for module, attr in imports_to_test:
        try:
            if attr:
                exec(f"from {module} import {attr}")
                print(f"✅ {module}.{attr} import successful")
            else:
                exec(f"import {module}")
                print(f"✅ {module} import successful")
        except ImportError as e:
            print(f"❌ {module} import failed: {e}")
            success = False
        except Exception as e:
            print(f"❌ {module} test failed: {e}")
            success = False
    
    return success

def main():
    """Main function to fix JWT issues."""
    print("🚀 JWT Fix Script")
    print("=" * 50)
    
    # Step 1: Install packages
    if not install_jwt_package():
        print("\n❌ Package installation failed. Please try manually:")
        print("pip install PyJWT>=2.8.0")
        return False
    
    # Step 2: Test JWT import
    if not test_jwt_import():
        print("\n❌ JWT import test failed.")
        return False
    
    # Step 3: Test other auth imports
    if not test_other_auth_imports():
        print("\n⚠️  Some auth imports failed, but JWT should work.")
    
    print("\n🎉 JWT fix completed successfully!")
    print("\nYou can now run your application:")
    print("python main.py")
    print("# OR")
    print("uvicorn main:app --reload")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
