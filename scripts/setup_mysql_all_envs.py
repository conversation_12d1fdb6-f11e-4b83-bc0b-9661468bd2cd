#!/usr/bin/env python3
"""
MySQL Database Setup Script for All Environments

This script sets up MySQL databases for development, test, and production environments.
It creates the necessary databases and users for each environment.
"""

import os
import sys
import getpass
import pymysql
from dotenv import load_dotenv
import argparse

# Load environment variables
load_dotenv()

def print_section(title):
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f"🗄️  {title}")
    print('='*60)

def print_step(step, description):
    """Print a step description."""
    print(f"\n{step}. {description}")

def get_mysql_root_credentials():
    """Get MySQL root credentials."""
    print_section("MySQL Root Access")
    print("Please provide MySQL root credentials to set up all environments:")
    
    host = input("MySQL Host [localhost]: ").strip() or "localhost"
    port = input("MySQL Port [3306]: ").strip() or "3306"
    root_user = input("Root Username [root]: ").strip() or "root"
    root_password = getpass.getpass("Root Password: ")
    
    return {
        "host": host,
        "port": int(port),
        "root_user": root_user,
        "root_password": root_password
    }

def test_root_connection(credentials):
    """Test connection with root credentials."""
    print_step(1, "Testing MySQL root connection...")
    
    try:
        connection = pymysql.connect(
            host=credentials["host"],
            port=credentials["port"],
            user=credentials["root_user"],
            password=credentials["root_password"]
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"✅ Connected to MySQL {version}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ Root connection failed: {e}")
        return False

def create_database_and_user(credentials, db_config):
    """Create database and user for a specific environment."""
    env_name = db_config["env_name"]
    db_name = db_config["db_name"]
    username = db_config["username"]
    password = db_config["password"]
    
    print_step(f"Setting up {env_name.upper()}", f"Database: {db_name}, User: {username}")
    
    try:
        connection = pymysql.connect(
            host=credentials["host"],
            port=credentials["port"],
            user=credentials["root_user"],
            password=credentials["root_password"]
        )
        
        with connection.cursor() as cursor:
            # Create database if not exists
            cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
            if not cursor.fetchone():
                cursor.execute(f"""
                    CREATE DATABASE {db_name} 
                    CHARACTER SET utf8mb4 
                    COLLATE utf8mb4_unicode_ci
                """)
                print(f"✅ Created database: {db_name}")
            else:
                print(f"⚠️  Database '{db_name}' already exists")
            
            # Create user if not exists (for non-root users)
            if username != "root":
                cursor.execute(f"SELECT User FROM mysql.user WHERE User = '{username}'")
                if not cursor.fetchone():
                    cursor.execute(f"""
                        CREATE USER '{username}'@'%' 
                        IDENTIFIED BY '{password}'
                    """)
                    print(f"✅ Created user: {username}")
                else:
                    print(f"⚠️  User '{username}' already exists")
                    # Update password
                    cursor.execute(f"""
                        ALTER USER '{username}'@'%' 
                        IDENTIFIED BY '{password}'
                    """)
                    print(f"✅ Updated password for user: {username}")
                
                # Grant privileges
                cursor.execute(f"""
                    GRANT ALL PRIVILEGES ON {db_name}.* 
                    TO '{username}'@'%'
                """)
                print(f"✅ Granted privileges to {username} on {db_name}")
            
            cursor.execute("FLUSH PRIVILEGES")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to set up {env_name}: {e}")
        return False

def test_database_connection(credentials, db_config):
    """Test connection to a specific database."""
    env_name = db_config["env_name"]
    db_name = db_config["db_name"]
    username = db_config["username"]
    password = db_config["password"]
    
    try:
        connection = pymysql.connect(
            host=credentials["host"],
            port=credentials["port"],
            user=username,
            password=password,
            database=db_name
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT DATABASE()")
            current_db = cursor.fetchone()[0]
            print(f"✅ {env_name.upper()} connection successful: {current_db}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ {env_name.upper()} connection failed: {e}")
        return False

def generate_env_configurations(credentials):
    """Generate environment configurations for all environments."""
    print_step("Final", "Generating environment configurations...")
    
    host = credentials["host"]
    port = credentials["port"]
    
    # Database configurations
    databases = {
        "development": {
            "url": f"mysql+pymysql://root:{credentials['root_password']}@{host}:{port}/saas_dev_db",
            "description": "Development database (using root for simplicity)"
        },
        "test": {
            "url": f"mysql+pymysql://root:{credentials['root_password']}@{host}:{port}/saas_test_db",
            "description": "Test database (using root for simplicity)"
        },
        "production": {
            "url": f"mysql+pymysql://saas_user:CHANGE_THIS_PASSWORD@{host}:{port}/saas_prod_db",
            "description": "Production database (dedicated user)"
        }
    }
    
    env_config = f"""
# MySQL Database Configuration for All Environments
# Generated on: {__import__('datetime').datetime.now()}

# Development Database
DEV_DATABASE_URL={databases['development']['url']}

# Test Database  
TEST_DATABASE_URL={databases['test']['url']}

# Production Database (CHANGE THE PASSWORD!)
PROD_DATABASE_URL={databases['production']['url']}

# Database Pool Settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_RECYCLE=3600
DB_CONNECT_TIMEOUT=10
DB_READ_TIMEOUT=30
DB_WRITE_TIMEOUT=30

# MySQL SSL Configuration (for production)
DB_SSL_CA=
DB_SSL_CERT=
DB_SSL_KEY=
DB_SSL_VERIFY_CERT=true
"""
    
    print("✅ Environment configurations generated:")
    print(env_config)
    
    # Save to file
    with open(".env.mysql_all", "w") as f:
        f.write(env_config.strip())
    
    print("💾 Configuration saved to .env.mysql_all")
    print("📝 Add these settings to your .env file")

def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(description="MySQL Setup for All Environments")
    parser.add_argument("--env", choices=["dev", "test", "prod", "all"], default="all",
                       help="Environment to set up (default: all)")
    args = parser.parse_args()
    
    print_section("MySQL Multi-Environment Setup")
    print("This script will set up MySQL databases for all environments")
    
    # Get root credentials
    credentials = get_mysql_root_credentials()
    
    # Test root connection
    if not test_root_connection(credentials):
        print("❌ Cannot proceed without valid root connection")
        return 1
    
    # Database configurations for each environment
    db_configs = [
        {
            "env_name": "development",
            "db_name": "saas_dev_db",
            "username": "root",  # Using root for development simplicity
            "password": credentials["root_password"]
        },
        {
            "env_name": "test", 
            "db_name": "saas_test_db",
            "username": "root",  # Using root for test simplicity
            "password": credentials["root_password"]
        },
        {
            "env_name": "production",
            "db_name": "saas_prod_db", 
            "username": "saas_user",
            "password": "CHANGE_THIS_PASSWORD"  # Will be prompted in production setup
        }
    ]
    
    # Filter configurations based on argument
    if args.env != "all":
        env_map = {"dev": "development", "test": "test", "prod": "production"}
        target_env = env_map[args.env]
        db_configs = [config for config in db_configs if config["env_name"] == target_env]
    
    # Set up each environment
    success_count = 0
    for db_config in db_configs:
        if create_database_and_user(credentials, db_config):
            if test_database_connection(credentials, db_config):
                success_count += 1
    
    # Generate configurations
    if success_count > 0:
        generate_env_configurations(credentials)
    
    # Summary
    print_section("Setup Summary")
    total = len(db_configs)
    print(f"Environments set up: {success_count}/{total}")
    
    if success_count == total:
        print("🎉 All environments set up successfully!")
        print("\nNext steps:")
        print("1. Update your .env file with the generated configuration")
        print("2. For production, change the saas_user password")
        print("3. Test your application with: python test_mysql.py")
        return 0
    else:
        print("⚠️  Some environments failed to set up")
        return 1

if __name__ == "__main__":
    sys.exit(main())
