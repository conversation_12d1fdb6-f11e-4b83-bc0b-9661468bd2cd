#!/bin/bash

echo "🔧 Rebuilding Docker containers with fixes..."

# Stop any running containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose.dev.yml down

# Remove any cached images to force rebuild
echo "🗑️  Removing cached images..."
docker-compose -f docker-compose.dev.yml down --rmi all

# Rebuild and start containers
echo "🏗️  Rebuilding containers..."
docker-compose -f docker-compose.dev.yml up --build --detach

# Wait a moment for containers to start
echo "⏳ Waiting for containers to start..."
sleep 10

# Check container status
echo "📊 Container status:"
docker-compose -f docker-compose.dev.yml ps

# Test the middleware imports
echo "🧪 Testing middleware imports..."
docker-compose -f docker-compose.dev.yml exec app python test_middleware_fix.py

# Check application logs
echo "📋 Application logs:"
docker-compose -f docker-compose.dev.yml logs app --tail=20

echo "✅ Rebuild complete!"
