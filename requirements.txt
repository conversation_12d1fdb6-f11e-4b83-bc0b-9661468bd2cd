# FastAPI and Core Dependencies
fastapi
starlette
pydantic[email]
uvicorn
python-dotenv
jinja2

# Database Dependencies
sqlalchemy>=2.0.0
alembic
psycopg2-binary  # PostgreSQL driver
pymysql          # MySQL driver
cryptography     # Required for MySQL SSL connections

# AI and LangChain Dependencies
openai>=1.0.0
langchain-community
langchain
langchain-openai

# Authentication & Security
passlib[bcrypt]>=1.7.4
python-jose[cryptography]
python-multipart
PyJWT>=2.8.0
bcrypt>=4.0.0,<5.0.0

# Additional Utilities
email-validator
python-dateutil
redis>=4.5.0

# Production Server Dependencies
gunicorn>=21.2.0
gevent>=23.0.0
psutil>=5.9.0

# Monitoring and Health Checks
prometheus-client>=0.17.0

# Testing Dependencies
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
httpx>=0.24.0
factory-boy>=3.3.0
faker>=19.0.0

# Security Dependencies
cryptography>=41.0.0
bleach>=6.0.0
user-agents>=2.2.0
python-multipart>=0.0.6
