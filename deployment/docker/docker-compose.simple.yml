version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.simple
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=development
      - JWT_SECRET_KEY=dev-jwt-secret-key-not-for-production-use-only
      - DATABASE_URL=mysql+pymysql://saas_user:saas_password@mysql_dev:3306/saas_dev_db
      - DEV_DATABASE_URL=mysql+pymysql://saas_user:saas_password@mysql_dev:3306/saas_dev_db
      - TEST_DATABASE_URL=mysql+pymysql://saas_user:saas_password@mysql_dev:3306/saas_test_db
      - PROD_DATABASE_URL=mysql+pymysql://saas_user:saas_password@mysql_dev:3306/saas_prod_db
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
    volumes:
      - .:/app
    depends_on:
      mysql_dev:
        condition: service_healthy
    restart: unless-stopped

  mysql_dev:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: saas_dev_db
      MYSQL_USER: saas_user
      MYSQL_PASSWORD: saas_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --max_connections=100
      --innodb_buffer_pool_size=128M
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    restart: unless-stopped

volumes:
  mysql_dev_data:
