services:
  app:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ../..:/app
    environment:
      # Development environment
      APP_ENV: development

      # MySQL Development Database
      DEV_DATABASE_URL: mysql+pymysql://dev_user:dev_password@mysql_dev:3306/saas_dev_db

      # Authentication
      JWT_SECRET_KEY: dev-jwt-secret-key-not-for-production
      OPENAI_API_KEY: ${OPENAI_API_KEY}

      # Database Pool Settings
      DB_POOL_SIZE: 5
      DB_MAX_OVERFLOW: 10

    depends_on:
      mysql_dev:
        condition: service_healthy
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  mysql_dev:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root_dev_password
      MYSQL_DATABASE: saas_dev_db
      MYSQL_USER: dev_user
      MYSQL_PASSWORD: dev_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --max_connections=100
      --innodb_buffer_pool_size=128M
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

volumes:
  mysql_dev_data:
