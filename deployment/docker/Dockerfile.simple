# Lightweight Dockerfile for quick testing
FROM python:3.11-slim

# Set the working directory in the container
WORKDIR /app

# Install system dependencies needed for Python packages
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies with all required packages
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir \
    fastapi \
    uvicorn[standard] \
    python-dotenv \
    sqlalchemy>=2.0.0 \
    pymysql \
    cryptography \
    PyJWT>=2.8.0 \
    passlib[bcrypt] \
    python-jose[cryptography] \
    python-multipart \
    bcrypt \
    bleach>=6.0.0 \
    user-agents>=2.2.0 \
    langchain \
    langchain-community \
    langchain-openai \
    openai \
    jinja2 \
    pydantic[email] \
    email-validator \
    starlette

# Copy the rest of the application code
COPY . .

# Expose the port that FastAPI runs on
EXPOSE 8000

# Command to run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
