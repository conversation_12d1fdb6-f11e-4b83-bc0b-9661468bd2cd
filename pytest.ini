[tool:pytest]
# Pytest configuration for SaaS Backend testing

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=.
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    --durations=10

# Markers for test categorization
markers =
    unit: Unit tests (fast, isolated)
    integration: Integration tests (slower, with external dependencies)
    api: API endpoint tests
    database: Database-related tests
    auth: Authentication and authorization tests
    ai: AI/OpenAI integration tests
    slow: Slow tests (may take more than 1 second)
    security: Security-related tests
    performance: Performance tests

# Test environment
env = 
    APP_ENV = test
    TEST_DATABASE_URL = mysql+pymysql://root:@localhost:3306/saas_test_db
    JWT_SECRET_KEY = test-jwt-secret-key-for-testing-only
    OPENAI_API_KEY = test-openai-key
    LOG_LEVEL = WARNING
    ENABLE_RATE_LIMITING = false
    SQL_ECHO = false

# Async support
asyncio_mode = auto

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning
