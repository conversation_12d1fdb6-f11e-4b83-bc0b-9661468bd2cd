{% extends "base.html" %}

{% block title %}Messages - Admin{% endblock %}
{% block page_title %}Messages Management{% endblock %}

{% block content %}
<div class="row">
    <!-- Main Content -->
    <div class="col-lg-9">
        <!-- Messages Table -->
        <div class="card shadow-sm">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    Messages ({{ messages|length }} found)
                </h6>
            </div>
            <div class="card-body p-0">
                {% if messages %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Role</th>
                                    <th>Content</th>
                                    <th>Chat</th>
                                    <th>User</th>
                                    <th>Created</th>
                                    <th class="table-actions">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for message in messages %}
                                <tr>
                                    <td>
                                        <code class="text-muted small">{{ message.id }}</code>
                                    </td>
                                    <td>
                                        {% if message.role == 'user' %}
                                            <span class="badge bg-primary">
                                                <i class="bi bi-person me-1"></i>User
                                            </span>
                                        {% else %}
                                            <span class="badge bg-success">
                                                <i class="bi bi-robot me-1"></i>Assistant
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div style="max-width: 300px;">
                                            <div class="text-truncate">{{ message.content }}</div>
                                            {% if message.content|length > 50 %}
                                                <small class="text-muted">{{ message.content|length }} characters</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <a href="/admin/chats/{{ message.chat_id }}" class="text-decoration-none">
                                            {{ message.chat_title }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ message.user_name }}</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ message.created_at.strftime('%Y-%m-%d %H:%M') if message.created_at else 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-primary btn-sm" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#messageModal{{ message.id }}" 
                                                    title="View Full Message">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-chat-text text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">No messages found</h5>
                        <p class="text-muted">No messages match your current filters.</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Total Count -->
        {% if messages %}
        <div class="mt-3 text-center">
            <small class="text-muted">Total: {{ messages|length }} messages</small>
        </div>
        {% endif %}
    </div>

    <!-- Sidebar Filters -->
    <div class="col-lg-3">
        <div class="card shadow-sm">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-funnel me-2"></i>Filters
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="/admin/messages">
                    <!-- Search -->
                    <div class="mb-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request.query_params.get('search', '') }}" 
                               placeholder="Search message content...">
                    </div>

                    <!-- Role Filter -->
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role">
                            <option value="">All Roles</option>
                            <option value="user" {% if request.query_params.get('role') == 'user' %}selected{% endif %}>User</option>
                            <option value="assistant" {% if request.query_params.get('role') == 'assistant' %}selected{% endif %}>Assistant</option>
                        </select>
                    </div>

                    <!-- Chat Filter -->
                    <div class="mb-3">
                        <label for="chat_id" class="form-label">Chat</label>
                        <select class="form-select" id="chat_id" name="chat_id">
                            <option value="">All Chats</option>
                            {% for chat in all_chats %}
                                <option value="{{ chat.id }}" 
                                        {% if request.query_params.get('chat_id') == chat.id|string %}selected{% endif %}>
                                    {{ chat.title }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Filter Buttons -->
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search me-2"></i>Apply Filters
                        </button>
                        <a href="/admin/messages" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-2"></i>Clear Filters
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card shadow-sm mt-3">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-bar-chart me-2"></i>Quick Stats
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-2">
                        <div class="border rounded p-2">
                            <div class="h5 mb-0 text-primary">{{ messages|length }}</div>
                            <small class="text-muted">Total Messages</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Message Detail Modals -->
{% for message in messages %}
<div class="modal fade" id="messageModal{{ message.id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    {% if message.role == 'user' %}
                        <i class="bi bi-person-circle text-primary me-2"></i>User Message
                    {% else %}
                        <i class="bi bi-robot text-success me-2"></i>Assistant Message
                    {% endif %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>Content:</strong>
                    <div class="mt-2 p-3 bg-light rounded">{{ message.content }}</div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <strong>Chat:</strong> {{ message.chat_title }}
                    </div>
                    <div class="col-md-6">
                        <strong>User:</strong> {{ message.user_name }}
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-md-6">
                        <strong>Created:</strong> {{ message.created_at.strftime('%Y-%m-%d %H:%M:%S') if message.created_at else 'N/A' }}
                    </div>
                    <div class="col-md-6">
                        <strong>ID:</strong> <code>{{ message.id }}</code>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="/admin/chats/{{ message.chat_id }}" class="btn btn-primary">View Chat</a>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}

{% block extra_css %}
<style>
.table-actions {
    width: 80px;
}
</style>
{% endblock %}
