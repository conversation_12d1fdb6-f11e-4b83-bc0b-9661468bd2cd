{% extends "base.html" %}

{% block title %}{{ 'Edit' if admin_user else 'Create' }} Admin User - Admin{% endblock %}
{% block page_title %}{{ 'Edit' if admin_user else 'Create' }} Admin User{% endblock %}

{% block page_actions %}
<a href="/admin/admin-users" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Admin Users
</a>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    {{ 'Edit Admin User Information' if admin_user else 'Create New Admin User' }}
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                Full Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control {% if errors.name %}is-invalid{% endif %}" 
                                   id="name" 
                                   name="name" 
                                   value="{{ admin_user.name if admin_user else form_data.get('name', '') }}"
                                   required>
                            {% if errors.name %}
                                <div class="invalid-feedback">{{ errors.name }}</div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                Email Address <span class="text-danger">*</span>
                            </label>
                            <input type="email" 
                                   class="form-control {% if errors.email %}is-invalid{% endif %}" 
                                   id="email" 
                                   name="email" 
                                   value="{{ admin_user.email if admin_user else form_data.get('email', '') }}"
                                   required>
                            {% if errors.email %}
                                <div class="invalid-feedback">{{ errors.email }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" 
                                   class="form-control {% if errors.phone %}is-invalid{% endif %}" 
                                   id="phone" 
                                   name="phone" 
                                   value="{{ admin_user.phone if admin_user else form_data.get('phone', '') }}"
                                   placeholder="******-123-4567">
                            {% if errors.phone %}
                                <div class="invalid-feedback">{{ errors.phone }}</div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">
                                Role <span class="text-danger">*</span>
                            </label>
                            <select class="form-select {% if errors.role %}is-invalid{% endif %}" 
                                    id="role" 
                                    name="role"
                                    required>
                                <option value="">Select role</option>
                                <option value="super_admin" 
                                        {% if (admin_user and admin_user.role == 'super_admin') or form_data.get('role') == 'super_admin' %}selected{% endif %}>
                                    Super Admin
                                </option>
                                <option value="admin" 
                                        {% if (admin_user and admin_user.role == 'admin') or form_data.get('role') == 'admin' %}selected{% endif %}>
                                    Admin
                                </option>
                                <option value="moderator" 
                                        {% if (admin_user and admin_user.role == 'moderator') or form_data.get('role') == 'moderator' %}selected{% endif %}>
                                    Moderator
                                </option>
                            </select>
                            {% if errors.role %}
                                <div class="invalid-feedback">{{ errors.role }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        {% if not admin_user %}
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">
                                Password <span class="text-danger">*</span>
                            </label>
                            <input type="password" 
                                   class="form-control {% if errors.password %}is-invalid{% endif %}" 
                                   id="password" 
                                   name="password" 
                                   required>
                            {% if errors.password %}
                                <div class="invalid-feedback">{{ errors.password }}</div>
                            {% endif %}
                            <div class="form-text">Minimum 8 characters required</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">
                                Confirm Password <span class="text-danger">*</span>
                            </label>
                            <input type="password" 
                                   class="form-control {% if errors.confirm_password %}is-invalid{% endif %}" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   required>
                            {% if errors.confirm_password %}
                                <div class="invalid-feedback">{{ errors.confirm_password }}</div>
                            {% endif %}
                        </div>
                        {% endif %}

                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select {% if errors.status %}is-invalid{% endif %}" 
                                    id="status" 
                                    name="status">
                                <option value="active" 
                                        {% if (admin_user and admin_user.status == 'active') or form_data.get('status') == 'active' or not admin_user %}selected{% endif %}>
                                    Active
                                </option>
                                <option value="inactive" 
                                        {% if (admin_user and admin_user.status == 'inactive') or form_data.get('status') == 'inactive' %}selected{% endif %}>
                                    Inactive
                                </option>
                            </select>
                            {% if errors.status %}
                                <div class="invalid-feedback">{{ errors.status }}</div>
                            {% endif %}
                        </div>
                    </div>

                    {% if admin_user %}
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Created</label>
                            <input type="text" class="form-control" 
                                   value="{{ admin_user.created_at.strftime('%Y-%m-%d %H:%M:%S') if admin_user.created_at else 'N/A' }}" 
                                   readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Last Updated</label>
                            <input type="text" class="form-control" 
                                   value="{{ admin_user.updated_at.strftime('%Y-%m-%d %H:%M:%S') if admin_user.updated_at else 'N/A' }}" 
                                   readonly>
                        </div>
                    </div>

                    <!-- Password Change Section -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="m-0">Change Password</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="new_password" class="form-label">New Password</label>
                                    <input type="password" 
                                           class="form-control {% if errors.new_password %}is-invalid{% endif %}" 
                                           id="new_password" 
                                           name="new_password">
                                    {% if errors.new_password %}
                                        <div class="invalid-feedback">{{ errors.new_password }}</div>
                                    {% endif %}
                                    <div class="form-text">Leave blank to keep current password</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="confirm_new_password" class="form-label">Confirm New Password</label>
                                    <input type="password" 
                                           class="form-control {% if errors.confirm_new_password %}is-invalid{% endif %}" 
                                           id="confirm_new_password" 
                                           name="confirm_new_password">
                                    {% if errors.confirm_new_password %}
                                        <div class="invalid-feedback">{{ errors.confirm_new_password }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="/admin/admin-users" class="btn btn-outline-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            {{ 'Update Admin User' if admin_user else 'Create Admin User' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
