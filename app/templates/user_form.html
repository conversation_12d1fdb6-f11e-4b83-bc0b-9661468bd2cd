{% extends "base.html" %}

{% block title %}{{ 'Edit' if user else 'Create' }} User - Admin{% endblock %}
{% block page_title %}{{ 'Edit' if user else 'Create' }} User{% endblock %}

{% block page_actions %}
<a href="/admin/users" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Users
</a>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    {{ 'Edit User Information' if user else 'Create New User' }}
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                Full Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control {% if errors.name %}is-invalid{% endif %}" 
                                   id="name" 
                                   name="name" 
                                   value="{{ user.name if user else form_data.get('name', '') }}"
                                   required>
                            {% if errors.name %}
                                <div class="invalid-feedback">{{ errors.name }}</div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                Email Address <span class="text-danger">*</span>
                            </label>
                            <input type="email" 
                                   class="form-control {% if errors.email %}is-invalid{% endif %}" 
                                   id="email" 
                                   name="email" 
                                   value="{{ user.email if user else form_data.get('email', '') }}"
                                   required>
                            {% if errors.email %}
                                <div class="invalid-feedback">{{ errors.email }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" 
                                   class="form-control {% if errors.phone %}is-invalid{% endif %}" 
                                   id="phone" 
                                   name="phone" 
                                   value="{{ user.phone if user else form_data.get('phone', '') }}"
                                   placeholder="******-123-4567">
                            {% if errors.phone %}
                                <div class="invalid-feedback">{{ errors.phone }}</div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">
                                Role <span class="text-danger">*</span>
                            </label>
                            <select class="form-select {% if errors.role %}is-invalid{% endif %}" 
                                    id="role" 
                                    name="role"
                                    required>
                                <option value="">Select role</option>
                                <option value="admin" 
                                        {% if (user and user.role == 'admin') or form_data.get('role') == 'admin' %}selected{% endif %}>
                                    Admin
                                </option>
                                <option value="analyst" 
                                        {% if (user and user.role == 'analyst') or form_data.get('role') == 'analyst' %}selected{% endif %}>
                                    Analyst
                                </option>
                                <option value="consumer" 
                                        {% if (user and user.role == 'consumer') or form_data.get('role') == 'consumer' %}selected{% endif %}>
                                    Consumer
                                </option>
                                <option value="viewer" 
                                        {% if (user and user.role == 'viewer') or form_data.get('role') == 'viewer' %}selected{% endif %}>
                                    Viewer
                                </option>
                            </select>
                            {% if errors.role %}
                                <div class="invalid-feedback">{{ errors.role }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="tenant_id" class="form-label">
                                Company <span class="text-danger">*</span>
                            </label>
                            <select class="form-select {% if errors.tenant_id %}is-invalid{% endif %}" 
                                    id="tenant_id" 
                                    name="tenant_id"
                                    required>
                                <option value="">Select company</option>
                                {% for tenant in tenants %}
                                    <option value="{{ tenant.id }}"
                                            {% if (user and user.tenant_id == tenant.id) or form_data.get('tenant_id') == tenant.id or request.query_params.get('tenant_id') == tenant.id %}selected{% endif %}>
                                        {{ tenant.employer_name }}
                                    </option>
                                {% endfor %}
                            </select>
                            {% if errors.tenant_id %}
                                <div class="invalid-feedback">{{ errors.tenant_id }}</div>
                            {% endif %}
                            <div class="form-text">
                                Don't see the company? <a href="/admin/tenants/new" target="_blank">Create a new tenant first</a>.
                            </div>
                        </div>
                    </div>

                    {% if user %}
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Created</label>
                            <input type="text" class="form-control" 
                                   value="{{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else 'N/A' }}" 
                                   readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Last Updated</label>
                            <input type="text" class="form-control" 
                                   value="{{ user.updated_at.strftime('%Y-%m-%d %H:%M:%S') if user.updated_at else 'N/A' }}" 
                                   readonly>
                        </div>
                    </div>
                    {% endif %}

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="/admin/users" class="btn btn-outline-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            {{ 'Update User' if user else 'Create User' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        {% if user %}
        <!-- User Activity Section (placeholder for future features) -->
        <div class="card shadow-sm mt-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">User Activity</h6>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="bi bi-activity text-muted" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-2">Activity tracking coming soon</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-select tenant if passed in URL
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const tenantId = urlParams.get('tenant_id');
    if (tenantId) {
        const tenantSelect = document.getElementById('tenant_id');
        if (tenantSelect) {
            tenantSelect.value = tenantId;
        }
    }
});
</script>
{% endblock %}
