{% extends "base.html" %}

{% block title %}Chat Details - Admin{% endblock %}
{% block page_title %}Chat Details{% endblock %}

{% block page_actions %}
<a href="/admin/chats" class="btn btn-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Chats
</a>
{% endblock %}

{% block content %}
{% if chat %}
<div class="row">
    <!-- Chat Information -->
    <div class="col-lg-4">
        <div class="card shadow-sm">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-chat-dots me-2"></i>Chat Information
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">ID</label>
                    <div><code>{{ chat.id }}</code></div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Title</label>
                    <div>{{ chat.title }}</div>
                </div>
                
                {% if chat.description %}
                <div class="mb-3">
                    <label class="form-label fw-bold">Description</label>
                    <div>{{ chat.description }}</div>
                </div>
                {% endif %}
                
                <div class="mb-3">
                    <label class="form-label fw-bold">User</label>
                    <div>
                        <div>{{ chat.user_name }}</div>
                        <small class="text-muted">{{ chat.user_email }}</small>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Created</label>
                    <div>{{ chat.created_at.strftime('%Y-%m-%d %H:%M:%S') if chat.created_at else 'N/A' }}</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Last Updated</label>
                    <div>{{ chat.updated_at.strftime('%Y-%m-%d %H:%M:%S') if chat.updated_at else 'N/A' }}</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Total Messages</label>
                    <div><span class="badge bg-info">{{ chat_messages|length }} messages</span></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Messages -->
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-chat-text me-2"></i>Messages ({{ chat_messages|length }})
                </h6>
            </div>
            <div class="card-body p-0">
                {% if chat_messages %}
                    <div class="chat-messages" style="max-height: 600px; overflow-y: auto;">
                        {% for message in chat_messages %}
                        <div class="message-item border-bottom p-3 {% if message.role == 'user' %}bg-light{% endif %}">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div class="d-flex align-items-center">
                                    {% if message.role == 'user' %}
                                        <i class="bi bi-person-circle text-primary me-2"></i>
                                        <span class="fw-bold text-primary">User</span>
                                    {% else %}
                                        <i class="bi bi-robot text-success me-2"></i>
                                        <span class="fw-bold text-success">Assistant</span>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    {{ message.created_at.strftime('%Y-%m-%d %H:%M:%S') if message.created_at else 'N/A' }}
                                </small>
                            </div>
                            
                            <div class="message-content">
                                <div class="mb-2">{{ message.content }}</div>
                                
                                {% if message.metadata_info and message.metadata_info.get('sql_query') %}
                                <div class="mt-2">
                                    <small class="text-muted fw-bold">SQL Query:</small>
                                    <pre class="bg-dark text-light p-2 rounded mt-1" style="font-size: 0.8rem;"><code>{{ message.metadata_info.sql_query }}</code></pre>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-chat-dots text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">No messages</h5>
                        <p class="text-muted">This chat doesn't have any messages yet.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body text-center py-5">
                <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                <h5 class="mt-3 text-warning">Chat Not Found</h5>
                <p class="text-muted">The requested chat could not be found.</p>
                <a href="/admin/chats" class="btn btn-primary">
                    <i class="bi bi-arrow-left me-2"></i>Back to Chats
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.message-item:last-child {
    border-bottom: none !important;
}

.chat-messages {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

pre code {
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>
{% endblock %}
