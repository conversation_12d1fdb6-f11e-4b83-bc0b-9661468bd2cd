{% extends "base.html" %}

{% block title %}Dashboard - Admin{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body text-center py-5">
                <i class="bi bi-speedometer2 text-primary" style="font-size: 4rem;"></i>
                <h3 class="mt-3 mb-2">Welcome to Admin Dashboard</h3>
                <p class="text-muted mb-4">Manage your tenants and users from the navigation menu</p>

                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="/admin/tenants" class="btn btn-primary">
                        <i class="bi bi-building me-2"></i>
                        Manage Tenants
                    </a>
                    <a href="/admin/users" class="btn btn-success">
                        <i class="bi bi-people me-2"></i>
                        Manage Users
                    </a>
                    <a href="/admin/chats" class="btn btn-info">
                        <i class="bi bi-chat-dots me-2"></i>
                        Manage Chats
                    </a>
                    <a href="/admin/messages" class="btn btn-warning">
                        <i class="bi bi-chat-text me-2"></i>
                        Manage Messages
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mt-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Tenants</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_tenants }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-building text-primary" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Total Users</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_users }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people text-success" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Chats</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_chats }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-chat-dots text-info" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Total Messages</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_messages }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-chat-text text-warning" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
</style>
{% endblock %}
