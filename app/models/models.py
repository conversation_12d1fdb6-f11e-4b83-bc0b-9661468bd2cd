"""
Database models for the SaaS application.

This module defines all SQLAlchemy models for the application including:
- User management
- Chat sessions
- Messages
- Database connections
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import uuid
from abc import ABC

Base = declarative_base()

# ============================================================================
# BASE MODELS TO ELIMINATE LOGICAL DUPLICATION
# ============================================================================

class TimestampMixin:
    """
    Mixin for audit timestamp fields.
    Eliminates duplication of created_at/updated_at pattern across all models.
    """
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class StatusMixin:
    """
    Mixin for status tracking.
    Eliminates duplication of status field pattern across models.
    """
    status = Column(String(20), nullable=False, default='active')  # 'active', 'inactive'

class EmailMixin:
    """
    Mixin for email fields.
    Eliminates duplication of email field pattern across models.
    """
    email = Column(String(255), nullable=False, index=True)

class BaseModel(Base, TimestampMixin):
    """
    Abstract base model with common patterns.
    Eliminates duplication of common model patterns.
    """
    __abstract__ = True

    id = Column(Integer, primary_key=True, autoincrement=True)

    def to_dict(self):
        """Convert model instance to dictionary."""
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}

    def __repr__(self):
        """Generic repr for all models."""
        return f"<{self.__class__.__name__}(id={self.id})>"


class User(Base):
    """User model for authentication and user management."""
    
    __tablename__ = "users"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=True)
    full_name = Column(String(255), nullable=True)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)
    
    # Note: User model is legacy - relationships removed as chats use tenant_user_id
    
    def __repr__(self):
        return f"<User(id={self.id}, email={self.email})>"


class DatabaseConnection(Base):
    """Model for storing tenant database connection configurations."""

    __tablename__ = "database_connections"

    # Note: Current schema uses VARCHAR(36) but user preference is INTEGER
    # Keeping VARCHAR(36) to match existing schema for now
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False)
    name = Column(String(255), nullable=False)  # User-friendly name for the connection
    db_type = Column(String(50), nullable=False)  # mysql, postgresql, sqlite, etc.
    host = Column(String(255), nullable=True)
    port = Column(Integer, nullable=True)
    database_name = Column(String(255), nullable=False)
    username = Column(String(255), nullable=True)
    password_encrypted = Column(Text, nullable=True)  # Encrypted password
    connection_string = Column(Text, nullable=True)  # Full connection string (encrypted)
    status = Column(String(20), nullable=False, default='active')  # 'active', 'inactive'
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_tested = Column(DateTime(timezone=True), nullable=True)

    # Enhanced metadata for AI training
    metadata_info = Column(JSON, nullable=True)  # Store table schemas, etc.
    business_context = Column(JSON, nullable=True)  # Business domain information
    ai_training_data = Column(JSON, nullable=True)  # AI-specific training context
    schema_last_updated = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    tenant = relationship("Tenant")
    chats = relationship("Chat", back_populates="database_connection")
    database_tables = relationship("DatabaseTable", back_populates="database_connection", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<DatabaseConnection(id={self.id}, name={self.name}, db_type={self.db_type})>"


class Chat(Base):
    """Chat session model."""

    __tablename__ = "chats"

    # Note: Schema uses INTEGER but keeping current structure for compatibility
    id = Column(Integer, primary_key=True, autoincrement=True)
    tenant_user_id = Column(Integer, ForeignKey("tenant_users.id"), nullable=False)
    database_connection_id = Column(String(36), ForeignKey("database_connections.id"), nullable=True)
    title = Column(String(500), nullable=False, default="New Chat")
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Chat settings
    settings = Column(JSON, nullable=True)  # Store chat-specific settings

    # Relationships
    tenant_user = relationship("TenantUser")
    database_connection = relationship("DatabaseConnection", back_populates="chats")
    messages = relationship("Message", back_populates="chat", cascade="all, delete-orphan", order_by="Message.created_at")
    
    def __repr__(self):
        return f"<Chat(id={self.id}, title={self.title}, tenant_user_id={self.tenant_user_id})>"


class Message(Base):
    """Message model for chat conversations."""

    __tablename__ = "messages"

    id = Column(Integer, primary_key=True, autoincrement=True)
    chat_id = Column(Integer, ForeignKey("chats.id"), nullable=False)
    tenant_user_id = Column(Integer, ForeignKey("tenant_users.id"), nullable=True)
    role = Column(String(20), nullable=False)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Message metadata
    metadata_info = Column(JSON, nullable=True)  # Additional metadata

    # Relationships
    chat = relationship("Chat", back_populates="messages")
    tenant_user = relationship("TenantUser")
    
    def __repr__(self):
        return f"<Message(id={self.id}, role={self.role}, chat_id={self.chat_id})>"


class Tenant(BaseModel, StatusMixin, EmailMixin):
    """Tenant model for multi-tenant architecture."""

    __tablename__ = "tenants"

    employer_name = Column(String(255), nullable=False, index=True)
    size = Column(String(50), nullable=True)  # e.g., 'small', 'medium', 'large', '1-10', '11-50', etc.
    phone = Column(String(20), nullable=True)  # Phone number for the tenant/company

    # Note: id, status, email, created_at, updated_at inherited from mixins

    # Relationships
    tenant_users = relationship("TenantUser", back_populates="tenant", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Tenant(id={self.id}, employer_name={self.employer_name})>"


class TenantUser(BaseModel, StatusMixin, EmailMixin):
    """Tenant user model for users within a tenant organization."""

    __tablename__ = "tenant_users"

    name = Column(String(255), nullable=False)
    role = Column(String(50), nullable=False)  # 'admin', 'analyst', 'consumer', etc.
    phone = Column(String(20), nullable=True)  # Phone number for the user
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_superuser = Column(Boolean, default=False)
    last_login = Column(DateTime(timezone=True), nullable=True)

    # Note: id, status, email, created_at, updated_at inherited from mixins

    # Relationships
    tenant = relationship("Tenant", back_populates="tenant_users")

    def __repr__(self):
        return f"<TenantUser(id={self.id}, name={self.name}, role={self.role}, tenant_id={self.tenant_id})>"


class AdminUser(BaseModel, StatusMixin):
    """Admin user model for system administrators."""

    __tablename__ = "admin_users"

    name = Column(String(255), nullable=False)
    phone = Column(String(20), nullable=True)  # Phone number for the admin user
    role = Column(String(50), nullable=False)  # 'super_admin', 'admin', 'moderator', etc.
    email = Column(String(255), nullable=False, unique=True, index=True)
    password = Column(String(255), nullable=False)  # Encrypted password for login

    # Note: id, status, created_at, updated_at inherited from mixins

    def __repr__(self):
        return f"<AdminUser(id={self.id}, name={self.name}, role={self.role}, status={self.status})>"


class QueryHistory(Base):
    """Model for tracking query history and analytics."""

    __tablename__ = "query_history"

    id = Column(Integer, primary_key=True, autoincrement=True)
    tenant_user_id = Column(Integer, ForeignKey("tenant_users.id"), nullable=False)
    database_connection_id = Column(String(36), ForeignKey("database_connections.id"), nullable=True)
    message_id = Column(Integer, ForeignKey("messages.id"), nullable=True)
    chat_id = Column(Integer, ForeignKey("chats.id"), nullable=True)
    natural_language_query = Column(Text, nullable=False)
    generated_sql = Column(Text, nullable=True)
    query_result = Column(JSON, nullable=True)
    execution_status = Column(String(20), nullable=False)  # 'success', 'error', 'timeout'
    execution_time = Column(Integer, nullable=True)  # in milliseconds
    rows_affected = Column(Integer, nullable=True)
    error_message = Column(Text, nullable=True)
    query_complexity = Column(String(20), nullable=True)  # 'simple', 'medium', 'complex'
    tables_involved = Column(JSON, nullable=True)  # List of tables used
    query_source = Column(String(20), nullable=False, default='direct')  # 'direct', 'chat', etc.
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    tenant_user = relationship("TenantUser")
    database_connection = relationship("DatabaseConnection")
    message = relationship("Message")
    chat = relationship("Chat")
    
    def __repr__(self):
        return f"<QueryHistory(id={self.id}, status={self.execution_status})>"


class DatabaseTable(Base):
    """Model for storing detailed information about tables in tenant databases."""

    __tablename__ = "database_tables"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    database_connection_id = Column(String(36), ForeignKey("database_connections.id"), nullable=False)
    table_name = Column(String(255), nullable=False)
    table_type = Column(String(50), nullable=True)  # 'table', 'view', 'materialized_view'

    # Business context
    business_name = Column(String(255), nullable=True)  # Human-readable name
    business_description = Column(Text, nullable=True)  # What this table represents
    business_domain = Column(String(100), nullable=True)  # e.g., 'sales', 'inventory', 'users'

    # Technical metadata
    row_count = Column(Integer, nullable=True)
    estimated_size_mb = Column(Float, nullable=True)
    last_analyzed = Column(DateTime(timezone=True), nullable=True)

    # AI training context
    common_queries = Column(JSON, nullable=True)  # Frequently asked questions about this table
    query_patterns = Column(JSON, nullable=True)  # Common SQL patterns for this table
    ai_description = Column(Text, nullable=True)  # AI-optimized description

    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    database_connection = relationship("DatabaseConnection", back_populates="database_tables")
    columns = relationship("DatabaseColumn", back_populates="table", cascade="all, delete-orphan")
    relationships = relationship("TableRelationship",
                               foreign_keys="TableRelationship.source_table_id",
                               back_populates="source_table")

    def __repr__(self):
        return f"<DatabaseTable(id={self.id}, name={self.table_name}, domain={self.business_domain})>"


class DatabaseColumn(Base):
    """Model for storing detailed information about columns in database tables."""

    __tablename__ = "database_columns"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    table_id = Column(String(36), ForeignKey("database_tables.id"), nullable=False)
    column_name = Column(String(255), nullable=False)

    # Technical metadata
    data_type = Column(String(100), nullable=False)
    is_nullable = Column(Boolean, nullable=False, default=True)
    is_primary_key = Column(Boolean, nullable=False, default=False)
    is_foreign_key = Column(Boolean, nullable=False, default=False)
    is_unique = Column(Boolean, nullable=False, default=False)
    is_indexed = Column(Boolean, nullable=False, default=False)
    default_value = Column(String(255), nullable=True)
    max_length = Column(Integer, nullable=True)

    # Business context
    business_name = Column(String(255), nullable=True)  # Human-readable name
    business_description = Column(Text, nullable=True)  # What this column represents
    data_category = Column(String(100), nullable=True)  # 'identifier', 'measurement', 'description', etc.

    # Data quality and patterns
    sample_values = Column(JSON, nullable=True)  # Example values for AI context
    value_patterns = Column(JSON, nullable=True)  # Common patterns or formats
    data_quality_notes = Column(Text, nullable=True)  # Known data quality issues

    # AI training context
    ai_description = Column(Text, nullable=True)  # AI-optimized description
    common_filters = Column(JSON, nullable=True)  # Common WHERE clause patterns
    aggregation_patterns = Column(JSON, nullable=True)  # Common GROUP BY/aggregation uses

    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    table = relationship("DatabaseTable", back_populates="columns")

    def __repr__(self):
        return f"<DatabaseColumn(id={self.id}, name={self.column_name}, type={self.data_type})>"


class TableRelationship(Base):
    """Model for storing relationships between database tables."""

    __tablename__ = "table_relationships"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    source_table_id = Column(String(36), ForeignKey("database_tables.id"), nullable=False)
    target_table_id = Column(String(36), ForeignKey("database_tables.id"), nullable=False)

    # Relationship metadata
    relationship_type = Column(String(50), nullable=False)  # 'one_to_one', 'one_to_many', 'many_to_many'
    source_columns = Column(JSON, nullable=False)  # List of source column names
    target_columns = Column(JSON, nullable=False)  # List of target column names

    # Business context
    relationship_name = Column(String(255), nullable=True)  # Human-readable relationship name
    business_description = Column(Text, nullable=True)  # What this relationship represents

    # AI training context
    join_patterns = Column(JSON, nullable=True)  # Common JOIN patterns for this relationship
    ai_description = Column(Text, nullable=True)  # AI-optimized description

    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    source_table = relationship("DatabaseTable", foreign_keys=[source_table_id])
    target_table = relationship("DatabaseTable", foreign_keys=[target_table_id])

    def __repr__(self):
        return f"<TableRelationship(id={self.id}, type={self.relationship_type})>"


class AITrainingFeedback(Base):
    """Model for storing AI training feedback and learning data."""

    __tablename__ = "ai_training_feedback"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    database_connection_id = Column(String(36), ForeignKey("database_connections.id"), nullable=False)
    tenant_user_id = Column(Integer, ForeignKey("tenant_users.id"), nullable=False)
    query_history_id = Column(Integer, ForeignKey("query_history.id"), nullable=True)

    # Original query context
    natural_language_query = Column(Text, nullable=False)
    generated_sql = Column(Text, nullable=True)
    ai_response = Column(Text, nullable=True)

    # Feedback data
    feedback_type = Column(String(50), nullable=False)  # 'positive', 'negative', 'correction', 'suggestion'
    feedback_score = Column(Integer, nullable=True)  # 1-5 rating
    user_feedback = Column(Text, nullable=True)  # User's written feedback
    corrected_sql = Column(Text, nullable=True)  # User-provided correct SQL
    expected_result = Column(JSON, nullable=True)  # What the user expected

    # Context for learning
    query_intent = Column(String(100), nullable=True)  # Classified intent
    tables_mentioned = Column(JSON, nullable=True)  # Tables referenced in query
    business_context = Column(Text, nullable=True)  # Business context of the query

    # AI learning metadata
    learning_priority = Column(String(20), nullable=False, default='medium')  # 'low', 'medium', 'high'
    processed_for_training = Column(Boolean, nullable=False, default=False)
    training_notes = Column(Text, nullable=True)

    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    database_connection = relationship("DatabaseConnection")
    tenant_user = relationship("TenantUser")
    query_history = relationship("QueryHistory")

    def __repr__(self):
        return f"<AITrainingFeedback(id={self.id}, type={self.feedback_type}, score={self.feedback_score})>"


class TenantAIProfile(Base):
    """Model for storing tenant-specific AI training profiles and preferences."""

    __tablename__ = "tenant_ai_profiles"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False)
    database_connection_id = Column(String(36), ForeignKey("database_connections.id"), nullable=False)

    # Business domain information
    business_domain = Column(String(100), nullable=True)  # 'ecommerce', 'healthcare', 'finance', etc.
    industry_vertical = Column(String(100), nullable=True)  # More specific industry
    business_description = Column(Text, nullable=True)  # What the business does

    # AI training preferences
    preferred_query_style = Column(String(50), nullable=True)  # 'simple', 'detailed', 'technical'
    response_tone = Column(String(50), nullable=True)  # 'formal', 'casual', 'technical'
    explanation_level = Column(String(50), nullable=True)  # 'basic', 'intermediate', 'advanced'

    # Domain-specific terminology
    business_glossary = Column(JSON, nullable=True)  # Custom terms and definitions
    common_abbreviations = Column(JSON, nullable=True)  # Domain-specific abbreviations
    preferred_terminology = Column(JSON, nullable=True)  # Preferred ways to refer to concepts

    # Learning patterns
    successful_query_patterns = Column(JSON, nullable=True)  # Patterns that work well
    problematic_areas = Column(JSON, nullable=True)  # Areas where AI struggles
    improvement_priorities = Column(JSON, nullable=True)  # What to focus on improving

    # Performance metrics
    query_success_rate = Column(Float, nullable=True)  # Overall success rate
    user_satisfaction_score = Column(Float, nullable=True)  # Average user satisfaction
    last_performance_review = Column(DateTime(timezone=True), nullable=True)

    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    tenant = relationship("Tenant")
    database_connection = relationship("DatabaseConnection")

    def __repr__(self):
        return f"<TenantAIProfile(id={self.id}, domain={self.business_domain}, success_rate={self.query_success_rate})>"


class APIKey(Base):
    """Model for managing API keys for external services."""

    __tablename__ = "api_keys"

    id = Column(Integer, primary_key=True, autoincrement=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False)
    service_name = Column(String(100), nullable=False)  # 'openai', 'anthropic', etc.
    key_name = Column(String(255), nullable=False)  # User-friendly name
    encrypted_key = Column(Text, nullable=False)  # Encrypted API key
    status = Column(String(20), nullable=False, default='active')  # 'active', 'inactive'
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_used = Column(DateTime(timezone=True), nullable=True)

    # Usage tracking
    usage_count = Column(Integer, default=0)
    monthly_usage = Column(JSON, nullable=True)  # Track monthly usage

    # Relationships
    tenant = relationship("Tenant")
    
    def __repr__(self):
        return f"<APIKey(id={self.id}, service={self.service_name}, user_id={self.user_id})>"
