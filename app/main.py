from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request, Form, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.templating import Jinja2Templates
# from fastapi.staticfiles import StaticFiles  # Not used yet
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import BaseModel, ValidationError, EmailStr, field_validator
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from contextlib import asynccontextmanager
import logging
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Import new authentication and security modules
from app.core.auth import (
    JWTManager, PasswordManager, Token, UserLogin, UserRegister,
    get_current_user, auth_rate_limiter, validate_email_format
)
from app.core.security_middleware import setup_security_middleware

# Import enhanced security modules
from app.core.input_validation import (
    Secure<PERSON>ser<PERSON><PERSON><PERSON><PERSON>, SecureUserLogin, SecureDatabaseQuery,
    SecurityValidator, InputSanitizer
)
from app.core.secrets_manager import environment_secrets, validate_production_secrets
from app.config.api_versioning import setup_api_versioning, get_api_version, APIVersion, APIVersionResponse
from app.core.enhanced_security import EnhancedSecurityMiddleware

# Legacy imports for backward compatibility
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from passlib.context import CryptContext
import secrets
import time
from collections import defaultdict
from app.services.query_agent import run_nl_to_sql, get_database_tables, get_table_schema
# UUID generation is now handled by database auto-generation
import re
from datetime import datetime
from app.services.ai_message_processor import ai_processor

# Import database components
from app.core.database import get_db, create_tables
from app.services.mysql_manager import get_mysql_manager, setup_mysql_database
# Models are imported in CRUD modules
from app.core.crud import (
    get_user_crud, get_chat_crud, get_message_crud,
    get_db_connection_crud, get_query_history_crud,
    get_tenant_crud, get_tenant_user_crud, get_admin_user_crud
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    try:
        # Initialize MySQL database for ALL environments
        env = os.getenv("APP_ENV", "development").lower()
        print(f"🔧 Setting up MySQL database for {env} environment...")

        # Setup MySQL database (this will create databases if they don't exist)
        if setup_mysql_database():
            print("✅ MySQL database setup completed")
        else:
            print("⚠️  MySQL setup had issues, but continuing...")

        # Create tables (this is idempotent - won't recreate existing tables)
        create_tables()
        print("✅ Database tables initialized successfully")

        # Test database connection for ALL environments
        manager = get_mysql_manager()
        connection_test = manager.test_connection()
        if connection_test["status"] == "success":
            print(f"✅ MySQL connection verified: {connection_test['mysql_version']}")
        else:
            print(f"⚠️  MySQL connection issue: {connection_test.get('error', 'Unknown error')}")

    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        raise

    yield

    # Shutdown (if needed)
    print("🔄 Application shutting down...")

app = FastAPI(
    title="My SaaS Backend",
    description="Natural Language to SQL API with Database Integration",
    lifespan=lifespan
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Setup security middleware
setup_security_middleware(app)

# Setup enhanced security features (only in production)
env = os.getenv("APP_ENV", "development").lower()
if env == "production":
    app.add_middleware(EnhancedSecurityMiddleware)
else:
    print("⚠️  Enhanced security middleware disabled in development mode")

# Setup API versioning
setup_api_versioning(app)

# Validate production secrets on startup
if os.getenv("APP_ENV", "").lower() == "production":
    if not validate_production_secrets():
        logger.error("Production secrets validation failed! Please check your configuration.")
        # In production, you might want to exit here
        # sys.exit(1)

# Setup templates
templates = Jinja2Templates(directory="app/templates")

# Authentication setup
security = HTTPBasic()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Admin credentials from environment variables
ADMIN_USERNAME = os.getenv("ADMIN_USERNAME", "admin")  # Default for development only
ADMIN_PASSWORD = os.getenv("ADMIN_PASSWORD", "admin123")  # Default for development only
ADMIN_PASSWORD_HASH = pwd_context.hash(ADMIN_PASSWORD)

# Simple rate limiting storage
rate_limit_storage = defaultdict(list)

def check_rate_limit(request: Request, max_requests: int = 100, window_seconds: int = 3600):
    """Simple rate limiting based on IP address."""
    client_ip = request.client.host
    current_time = time.time()

    # Clean old requests outside the window
    rate_limit_storage[client_ip] = [
        req_time for req_time in rate_limit_storage[client_ip]
        if current_time - req_time < window_seconds
    ]

    # Check if limit exceeded
    if len(rate_limit_storage[client_ip]) >= max_requests:
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded. Please try again later."
        )

    # Add current request
    rate_limit_storage[client_ip].append(current_time)

def verify_admin_credentials(credentials: HTTPBasicCredentials = Depends(security)):
    """Verify admin credentials for basic authentication."""
    is_correct_username = secrets.compare_digest(credentials.username, ADMIN_USERNAME)
    is_correct_password = pwd_context.verify(credentials.password, ADMIN_PASSWORD_HASH)

    if not (is_correct_username and is_correct_password):
        raise HTTPException(
            status_code=401,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username

# Global exception handlers
@app.exception_handler(SQLAlchemyError)
async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError):
    logger.error(f"Database error: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Database error occurred", "error": str(exc)}
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    logger.error(f"Validation error: {exc}")
    return JSONResponse(
        status_code=422,
        content={"detail": "Validation error", "errors": exc.errors()}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unexpected error: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error", "error": str(exc)}
    )

# CORS middleware is now configured in security_middleware.py

# Database initialization is now handled in the lifespan manager above

# Data Models
class QueryRequest(BaseModel):
    db_uri: str  # e.g., mysql+pymysql://user:pass@host:3306/dbname
    question: str

class ChatCreate(BaseModel):
    title: str = "New Chat"

class ChatUpdate(BaseModel):
    title: Optional[str] = None
    archived: Optional[bool] = None

# Tenant Models
class TenantCreate(BaseModel):
    employer_name: str
    email: EmailStr
    phone: Optional[str] = None
    size: Optional[str] = None

    @field_validator('employer_name')
    @classmethod
    def validate_employer_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Employer name cannot be empty')
        if len(v.strip()) < 2:
            raise ValueError('Employer name must be at least 2 characters')
        return v.strip()

class TenantUpdate(BaseModel):
    employer_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    size: Optional[str] = None

# Tenant User Models
class TenantUserCreate(BaseModel):
    name: str
    email: EmailStr
    phone: Optional[str] = None
    role: str
    tenant_id: int

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Name cannot be empty')
        if len(v.strip()) < 2:
            raise ValueError('Name must be at least 2 characters')
        return v.strip()

    @field_validator('role')
    @classmethod
    def validate_role(cls, v):
        allowed_roles = ['admin', 'analyst', 'consumer', 'viewer']
        if v not in allowed_roles:
            raise ValueError(f'Role must be one of: {", ".join(allowed_roles)}')
        return v

class TenantUserUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    role: Optional[str] = None
    tenant_id: Optional[str] = None
    title: Optional[str] = None
    archived: Optional[bool] = None

class MessageCreate(BaseModel):
    message: str
    role: str = "user"

class Message(BaseModel):
    id: int
    role: str  # "user" or "assistant"
    content: str
    created_at: datetime

class Chat(BaseModel):
    id: int
    title: str
    created_at: datetime
    updated_at: datetime
    archived: bool = False

class DatabaseQueryRequest(BaseModel):
    query: str
    selected_tables: List[str] = []

# Initialize CRUD instances (now using class-based approach)
user_crud = get_user_crud()
chat_crud = get_chat_crud()
message_crud = get_message_crud()
db_connection_crud = get_db_connection_crud()
query_history_crud = get_query_history_crud()

# Default database URI for demo (you can change this)
DEFAULT_DB_URI = "mysql+pymysql://user:password@localhost:3306/dev_db"

# All chat and message operations now use proper database storage via CRUD operations

# ============================================================================
# UTILITY FUNCTIONS FOR CODE DEDUPLICATION
# ============================================================================

def get_or_create_default_user(db: Session) -> int:
    """Get or create default tenant user for demo purposes. Returns user_id."""
    try:
        # Try to find existing default tenant user by email
        tenant_user_crud = get_tenant_user_crud()
        tenant_crud = get_tenant_crud()

        user = tenant_user_crud.get_tenant_user_by_email(db, "<EMAIL>")
        if not user:
            # Create default tenant first if it doesn't exist
            tenant = tenant_crud.get_tenant_by_email(db, "<EMAIL>")
            if not tenant:
                try:
                    tenant = tenant_crud.create_tenant(
                        db=db,
                        employer_name="Default Company",
                        email="<EMAIL>"
                    )
                except Exception as e:
                    # If tenant creation fails due to duplicate, try to get existing one
                    tenant = tenant_crud.get_tenant_by_email(db, "<EMAIL>")
                    if not tenant:
                        raise e

            # Create default tenant user
            try:
                user = tenant_user_crud.create_tenant_user(
                    db=db,
                    name="Default User",
                    email="<EMAIL>",
                    role="admin",
                    tenant_id=tenant.id
                )
            except Exception as e:
                # If user creation fails due to duplicate, try to get existing one
                user = tenant_user_crud.get_tenant_user_by_email(db, "<EMAIL>")
                if not user:
                    raise e
        return user.id
    except Exception as e:
        print(f"Warning: Could not create/get default user: {e}")
        return 1  # Return a default ID

def validate_email_format(email: str) -> Optional[str]:
    """Validate email format. Returns error message if invalid, None if valid."""
    if not email or not email.strip():
        return 'Email is required'
    if '@' not in email:
        return 'Invalid email format'
    return None

def validate_required_field(value: str, field_name: str) -> Optional[str]:
    """Validate required field. Returns error message if invalid, None if valid."""
    if not value or not value.strip():
        return f'{field_name} is required'
    return None

def create_error_template_response(template_name: str, request: Request, context: dict, errors: dict):
    """Create standardized error template response."""
    context.update({
        "request": request,
        "errors": errors
    })
    return templates.TemplateResponse(template_name, context)

def get_crud_instances():
    """Get all CRUD instances in one call."""
    return {
        'tenant_crud': get_tenant_crud(),
        'tenant_user_crud': get_tenant_user_crud(),
        'admin_user_crud': get_admin_user_crud()
    }

def validate_tenant_form(employer_name: str, email: str, phone: str = "", size: str = "") -> dict:
    """Validate tenant form data. Returns dict of errors."""
    errors = {}

    # Validate employer name
    if error := validate_required_field(employer_name, 'Company name'):
        errors['employer_name'] = error

    # Validate email
    if error := validate_email_format(email):
        errors['email'] = error

    return errors

def validate_user_form(name: str, email: str, role: str, tenant_id: int, phone: str = "") -> dict:
    """Validate user form data. Returns dict of errors."""
    errors = {}

    # Validate name
    if error := validate_required_field(name, 'Name'):
        errors['name'] = error

    # Validate email
    if error := validate_email_format(email):
        errors['email'] = error

    # Validate role
    if error := validate_required_field(role, 'Role'):
        errors['role'] = error

    # Validate tenant_id
    if not tenant_id:
        errors['tenant_id'] = 'Company is required'

    return errors

def validate_admin_user_form(name: str, email: str, role: str, password: str = "", confirm_password: str = "", is_update: bool = False) -> dict:
    """Validate admin user form data. Returns dict of errors."""
    errors = {}

    # Validate name
    if error := validate_required_field(name, 'Name'):
        errors['name'] = error

    # Validate email
    if error := validate_email_format(email):
        errors['email'] = error

    # Validate role
    if error := validate_required_field(role, 'Role'):
        errors['role'] = error

    # Password validation (required for create, optional for update)
    if not is_update or password:  # Required for create, or if provided for update
        if not password:
            errors['password'] = 'Password is required'
        elif len(password) < 8:
            errors['password'] = 'Password must be at least 8 characters'
        elif password != confirm_password:
            errors['confirm_password'] = 'Passwords do not match'

    return errors

def check_email_exists(db: Session, email: str, crud_instance, current_email: str = None) -> Optional[str]:
    """Check if email already exists. Returns error message if exists, None if available."""
    if current_email and email == current_email:
        return None  # Same email, no conflict

    if hasattr(crud_instance, 'get_tenant_by_email'):
        existing = crud_instance.get_tenant_by_email(db, email)
    elif hasattr(crud_instance, 'get_tenant_user_by_email'):
        existing = crud_instance.get_tenant_user_by_email(db, email)
    elif hasattr(crud_instance, 'get_admin_user_by_email'):
        existing = crud_instance.get_admin_user_by_email(db, email)
    else:
        return None

    return 'A user with this email already exists' if existing else None

# ============================================================================
# SQL QUERY PROCESSING UTILITIES (Eliminates SQL generation logic duplication)
# ============================================================================

class SQLQueryProcessor:
    """
    Centralized SQL query processing logic.
    Eliminates duplication of SQL generation and response formatting.
    """

    @staticmethod
    def process_natural_language_query(query: str, db_uri: str = None, context: str = "chat") -> dict:
        """
        Process natural language query and return standardized response.

        Args:
            query: Natural language query
            db_uri: Database URI (uses default if None)
            context: Context of the query ("chat", "direct", etc.)

        Returns:
            dict: Standardized response with sql_query, content, etc.
        """
        db_uri = db_uri or DEFAULT_DB_URI

        try:
            # Use AI-powered intent classification (replaces all hardcoded patterns)
            intent_result = ai_processor.classify_message_intent(
                message=query,
                context={"context_type": context, "timestamp": datetime.now().isoformat()}
            )

            # Handle non-database queries with AI-generated responses
            if not intent_result.get("requires_sql", False):
                ai_response = ai_processor.generate_dynamic_response(
                    message=query,
                    intent_data=intent_result,
                    context={"context_type": context}
                )

                return {
                    "sql_query": None,
                    "content": ai_response,
                    "message_type": intent_result.get("intent", "general").lower(),
                    "confidence": intent_result.get("confidence", 0.5),
                    "ai_reasoning": intent_result.get("reasoning", ""),
                    "success": True
                }

            # Generate SQL using LangChain
            sql_query = run_nl_to_sql(db_uri, query)

            if context == "chat":
                content = f"Here's the SQL query for your request:\n\n```sql\n{sql_query}\n```\n\n💡 **Tip:** You can ask follow-up questions or request modifications to this query!"
            else:
                content = "SQL query generated successfully. Note: Actual execution is disabled for safety."

            return {
                "sql_query": sql_query,
                "content": content,
                "message_type": "sql_generation",
                "success": True
            }

        except Exception as e:
            return {
                "sql_query": None,
                "content": f"Error generating SQL query: {str(e)}",
                "message_type": "error",
                "success": False,
                "error": str(e)
            }

    @staticmethod
    def is_greeting_or_welcome(message: str) -> bool:
        """Check if message is a greeting, conversational question, or non-database query."""
        message_lower = message.lower().strip()

        # Remove punctuation for better matching
        cleaned_message = re.sub(r'[^\w\s]', '', message_lower)

        # Simple greetings - exact matches
        simple_greetings = [
            'hi', 'hello', 'hey', 'there', 'hy', 'hii', 'helo', 'hai',
            'good morning', 'good afternoon', 'good evening', 'good night',
            'greetings', 'howdy', 'hi there', 'hello there', 'hey there',
            'hy good morning', 'hi good morning', 'hello good morning',
            'hy good afternoon', 'hi good afternoon', 'hello good afternoon',
            'hy good evening', 'hi good evening', 'hello good evening'
        ]

        # Conversational questions about the assistant
        assistant_questions = [
            'who are you', 'what are you', 'who r u', 'what r u',
            'what can you do', 'what do you do', 'how can you help',
            'what is this', 'what is this app', 'what is this application',
            'tell me about yourself', 'introduce yourself',
            'what are your capabilities', 'what can you help with',
            'help', 'help me', 'can you help', 'can you help me'
        ]

        # How are you variations
        how_are_you_patterns = [
            'how are you', 'how r u', 'how are u', 'how do you do',
            'how are things', 'how is it going', 'how are you doing',
            'whats up', 'what is up', 'sup', 'wassup'
        ]

        # Thank you messages
        thank_you_patterns = [
            'thank you', 'thanks', 'thank u', 'thx', 'ty',
            'thank you very much', 'thanks a lot', 'much appreciated'
        ]

        # Check for exact matches in all categories
        all_conversational = simple_greetings + assistant_questions + how_are_you_patterns + thank_you_patterns

        if cleaned_message in all_conversational:
            return True

        # Check for pattern matches with more flexibility
        greeting_patterns = [
            r'^(hi|hello|hey|hy|hii|hai)[\s!.?]*$',
            r'^(hi|hello|hey|hy)\s+(there|friend|buddy)[\s!.?]*$',
            r'^(good\s+)?(morning|afternoon|evening|night)[\s!.?]*$',
            r'^(hi|hello|hey|hy)\s+(good\s+)?(morning|afternoon|evening|night)[\s!.?]*$',
            r'^how\s+(are\s+you|r\s+u|are\s+u)[\s!.?]*$',
            r'^(who|what)\s+(are\s+you|r\s+u|are\s+u)[\s!.?]*$',
            r'^what\s+(can\s+you\s+do|do\s+you\s+do)[\s!.?]*$',
            r'^(thank\s+you|thanks|thx)[\s!.?]*$',
            r'^(help|can\s+you\s+help)[\s!.?]*$'
        ]

        for pattern in greeting_patterns:
            if re.match(pattern, cleaned_message):
                return True

        # Check if message contains greeting words (more flexible approach)
        greeting_words = ['hi', 'hello', 'hey', 'hy', 'hii', 'hai', 'helo']
        time_words = ['morning', 'afternoon', 'evening', 'night']

        words = cleaned_message.split()

        # If message contains both greeting and time words, it's likely a greeting
        has_greeting = any(word in greeting_words for word in words)
        has_time = any(word in time_words for word in words)

        if has_greeting and has_time and len(words) <= 4:
            return True

        # Check if message starts with greeting word
        if words and words[0] in greeting_words and len(words) <= 4:
            return True

        # Check if message is very short and likely conversational
        if len(message_lower.split()) <= 3:
            short_conversational = ['ok', 'okay', 'yes', 'no', 'sure', 'cool', 'nice', 'great']
            if cleaned_message in short_conversational:
                return True

        return False

    @staticmethod
    def generate_welcome_response(context: str = "chat") -> str:
        """Generate contextual welcome response."""
        if context == "chat":
            return "Hi! 👋 How can I help you today?"
        else:
            return "Welcome! I'm ready to help you with your database queries."
    
    @staticmethod
    def classify_message_intent(message: str) -> dict:
        """
        AI-powered message intent classification.
        Determines if a message is a database query, greeting, or general conversation.
        """
        message_lower = message.lower().strip()

        # Database query patterns (more comprehensive than hardcoded keywords)
        database_patterns = [
            # Data retrieval patterns
            r'\b(show|display|list|get|fetch|find|retrieve|select|query)\b.*\b(users?|customers?|products?|orders?|data|records?|table|database)\b',
            r'\b(how many|count|total|number of)\b.*\b(users?|customers?|products?|orders?|records?)\b',
            r'\b(what|which|who)\b.*\b(users?|customers?|products?|orders?|data)\b',
            r'\b(give me|show me|tell me about)\b.*\b(users?|customers?|products?|orders?|data)\b',
            r'\b(i want to see|i need|i would like)\b.*\b(users?|customers?|products?|orders?|data|information)\b',
            r'\b(see|view|check)\b.*\b(users?|customers?|products?|orders?|data|records?|table)\b',

            # SQL-like patterns
            r'\b(select|from|where|join|group by|order by|having|insert|update|delete|create|drop|alter)\b',

            # Data analysis patterns
            r'\b(analyze|analysis|report|statistics|stats|metrics|average|sum|maximum|minimum|aggregate)\b',

            # Database specific terms
            r'\b(table|database|schema|column|field|row|export|import|backup|restore)\b',

            # Intent patterns
            r'\b(all|every|entire)\b.*\b(users?|customers?|products?|orders?|records?)\b',
            r'\b(sales|revenue|profit|income)\b.*\b(data|information|report|analysis)\b'
        ]

        # Check pattern matches
        db_pattern_matches = sum(1 for pattern in database_patterns if re.search(pattern, message_lower))

        # Additional scoring factors
        has_question_words = bool(re.search(r'\b(what|how|when|where|which|who|why)\b', message_lower))
        has_data_words = bool(re.search(r'\b(data|information|details|records?|entries|items)\b', message_lower))
        is_question = message.strip().endswith('?')
        message_length = len(message.split())

        # Calculate database query probability
        db_score = 0
        db_score += db_pattern_matches * 3  # Pattern matches are strong indicators
        db_score += 2 if has_question_words and has_data_words else 0
        db_score += 1 if is_question and has_data_words else 0
        db_score += 1 if message_length > 5 else 0  # Longer messages more likely to be queries

        # Normalize score to confidence percentage
        confidence = min(db_score / 10, 1.0)
        is_database_query = db_score >= 3

        return {
            "is_database_query": is_database_query,
            "is_greeting": SQLQueryProcessor.is_greeting_or_welcome(message),
            "confidence": confidence,
            "pattern_matches": db_pattern_matches,
            "message_type": "database_query" if is_database_query else ("greeting" if SQLQueryProcessor.is_greeting_or_welcome(message) else "general")
        }

    @staticmethod
    def generate_conversational_response(message: str) -> str:
        """Generate appropriate conversational response based on message type."""
        message_lower = message.lower().strip()
        cleaned_message = re.sub(r'[^\w\s]', '', message_lower)
        
        # How are you responses
        how_are_you_patterns = [
            'how are you', 'how r u', 'how are u', 'how do you do',
            'how are things', 'how is it going', 'how are you doing',
            'whats up', 'what is up', 'sup', 'wassup'
        ]
        
        if any(pattern in cleaned_message for pattern in how_are_you_patterns):
            return "I'm doing great, thank you for asking! 😊 How can I help you with your database queries today?"
        
        # Who are you / What are you responses
        identity_patterns = [
            'who are you', 'what are you', 'who r u', 'what r u',
            'tell me about yourself', 'introduce yourself'
        ]
        
        if any(pattern in cleaned_message for pattern in identity_patterns):
            return ("I'm your AI database assistant! 🤖 I can help you query your database using natural language. "
                   "Just ask me questions about your data, and I'll convert them into SQL queries for you. "
                   "For example, you can ask 'Show me all users' or 'How many orders were placed today?'")
        
        # Capability questions
        capability_patterns = [
            'what can you do', 'what do you do', 'how can you help',
            'what are your capabilities', 'what can you help with'
        ]
        
        if any(pattern in cleaned_message for pattern in capability_patterns):
            return ("I can help you with database queries! 📊 Here's what I can do:\n\n"
                   "• Convert natural language questions into SQL queries\n"
                   "• Help you explore your database tables and data\n"
                   "• Answer questions about your data in plain English\n"
                   "• Provide insights and analytics from your database\n\n"
                   "Just ask me anything about your data, and I'll help you find the answers!")
        
        # Help requests
        help_patterns = ['help', 'help me', 'can you help', 'can you help me']
        
        if cleaned_message in help_patterns:
            return ("Of course! I'm here to help you with your database queries. 🚀\n\n"
                   "You can ask me questions like:\n"
                   "• 'Show me all users'\n"
                   "• 'How many products do we have?'\n"
                   "• 'List recent orders'\n"
                   "• 'Find customers from New York'\n\n"
                   "What would you like to know about your data?")
        
        # Thank you responses
        thank_you_patterns = [
            'thank you', 'thanks', 'thank u', 'thx', 'ty',
            'thank you very much', 'thanks a lot', 'much appreciated'
        ]
        
        if any(pattern in cleaned_message for pattern in thank_you_patterns):
            return "You're welcome! 😊 Feel free to ask me anything else about your database."
        
        # Default greeting response
        return "Hello! 👋 I'm your AI database assistant. How can I help you with your data today?"




# ============================================================================
# ADMIN FORM PROCESSING UTILITIES (Eliminates form processing logic duplication)
# ============================================================================

class AdminFormProcessor:
    """
    Centralized admin form processing logic.
    Eliminates duplication of form processing workflow across admin routes.
    """

    @staticmethod
    def process_admin_form(
        request: Request,
        db: Session,
        template_name: str,
        form_data: dict,
        validation_func: callable,
        creation_func: callable,
        success_redirect: str,
        context_data: dict = None
    ):
        """
        Generic admin form processing workflow.

        Args:
            request: FastAPI request object
            db: Database session
            template_name: Template to render
            form_data: Form data dictionary
            validation_func: Function to validate form data
            creation_func: Function to create entity
            success_redirect: URL to redirect on success
            context_data: Additional context data for template

        Returns:
            Response (redirect on success, template on error)
        """
        try:
            # Validate form data
            errors = validation_func(form_data, db)

            if errors:
                context = {
                    "form_data": form_data,
                    **(context_data or {})
                }
                return create_error_template_response(template_name, request, context, errors)

            # Create entity
            entity = creation_func(form_data, db)

            # Redirect on success
            return RedirectResponse(url=success_redirect, status_code=303)

        except Exception as e:
            context = {
                "form_data": form_data,
                **(context_data or {})
            }
            return create_error_template_response(
                template_name,
                request,
                context,
                {"general": f"Error processing form: {str(e)}"}
            )

    @staticmethod
    def create_tenant_validation_and_creation():
        """Create tenant-specific validation and creation functions."""
        def validate_tenant_form_data(form_data: dict, db: Session) -> dict:
            errors = validate_tenant_form(
                form_data['employer_name'],
                form_data['email'],
                form_data.get('phone', ''),
                form_data.get('size', '')
            )

            # Check if email exists
            tenant_crud = get_crud_instances()['tenant_crud']
            if email_error := check_email_exists(db, form_data['email'], tenant_crud):
                errors['email'] = email_error

            return errors

        def create_tenant_entity(form_data: dict, db: Session):
            tenant_crud = get_crud_instances()['tenant_crud']
            return tenant_crud.create_tenant(
                db=db,
                employer_name=form_data['employer_name'],
                email=form_data['email'],
                phone=form_data.get('phone') if form_data.get('phone') else None,
                size=form_data.get('size') if form_data.get('size') else None
            )

        return validate_tenant_form_data, create_tenant_entity

# Chat Management Endpoints
@app.get("/chats")
def get_chats(include_archived: bool = False, db: Session = Depends(get_db)):
    """Get all chats, optionally including archived ones"""
    # For now, we'll use a default user ID. In production, this would come from authentication
    default_user_id = "default-user"

    # Create default user if not exists
    user = user_crud.get_user_by_id(db, default_user_id)
    if not user:
        try:
            user = user_crud.create_user(db, email="<EMAIL>", password="default", full_name="Default User")
            user.id = default_user_id
            db.commit()
        except Exception as e:
            # If user creation fails, try to get existing user
            db.rollback()
            user = user_crud.get_user_by_email(db, "<EMAIL>")
            if not user:
                # Create a minimal user object for fallback
                from models import User
                user = User(id=default_user_id, email="<EMAIL>", full_name="Default User", hashed_password="dummy")

    chats = chat_crud.get_user_chats(db, default_user_id, include_archived)
    return [{"id": chat.id, "title": chat.title, "created_at": chat.created_at,
             "updated_at": chat.updated_at, "archived": False} for chat in chats]

@app.post("/chats")
def create_chat(chat_data: ChatCreate, db: Session = Depends(get_db)):
    """Create a new chat"""
    # Get or create default user for demo purposes
    default_user_id = get_or_create_default_user(db)

    # Create chat using database
    chat = chat_crud.create_chat(db, default_user_id, chat_data.title)

    return {
        "id": chat.id,
        "title": chat.title,
        "created_at": chat.created_at,
        "updated_at": chat.updated_at,
        "archived": False
    }

@app.get("/chats/{chat_id}")
def get_chat(chat_id: int, db: Session = Depends(get_db)):
    """Get specific chat"""
    chat = chat_crud.get_chat_by_id(db, chat_id)
    if not chat:
        raise HTTPException(status_code=404, detail="Chat not found")

    return {
        "id": chat.id,
        "title": chat.title,
        "created_at": chat.created_at,
        "updated_at": chat.updated_at,
        "archived": False
    }

@app.put("/chats/{chat_id}")
def update_chat(chat_id: int, chat_update: ChatUpdate, db: Session = Depends(get_db)):
    """Update chat title and/or archived status"""
    # Get or create default user for demo purposes
    default_user_id = get_or_create_default_user(db)

    # Prepare update data
    update_data = {}
    if chat_update.title is not None:
        update_data["title"] = chat_update.title
    if chat_update.archived is not None:
        update_data["is_archived"] = chat_update.archived

    # Update chat using database
    chat = chat_crud.update_chat(db, chat_id, default_user_id, **update_data)
    if not chat:
        raise HTTPException(status_code=404, detail="Chat not found")

    return {
        "id": chat.id,
        "title": chat.title,
        "created_at": chat.created_at,
        "updated_at": chat.updated_at,
        "archived": False
    }

@app.delete("/chats/{chat_id}")
def delete_chat(chat_id: int, db: Session = Depends(get_db)):
    """Delete chat permanently"""
    # Get or create default user for demo purposes
    default_user_id = get_or_create_default_user(db)

    # Delete chat using database
    success = chat_crud.delete_chat(db, chat_id, default_user_id)
    if not success:
        raise HTTPException(status_code=404, detail="Chat not found")

    return {"message": "Chat deleted successfully"}

@app.post("/chats/{chat_id}/archive")
def archive_chat(chat_id: int, db: Session = Depends(get_db)):
    """Archive a chat"""
    # Get or create default user for demo purposes
    default_user_id = get_or_create_default_user(db)

    chat = chat_crud.get_chat_by_id(db, chat_id)
    if not chat:
        raise HTTPException(status_code=404, detail="Chat not found")

    # For now, just return success without actual archiving logic
    # TODO: Implement proper archiving using archived_chats table
    return {
        "message": "Chat archived successfully",
        "chat": {
            "id": chat.id,
            "title": chat.title,
            "created_at": chat.created_at,
            "updated_at": chat.updated_at,
            "archived": True
        }
    }

@app.post("/chats/{chat_id}/unarchive")
def unarchive_chat(chat_id: int, db: Session = Depends(get_db)):
    """Unarchive a chat"""
    # Get or create default user for demo purposes
    default_user_id = get_or_create_default_user(db)

    chat = chat_crud.get_chat_by_id(db, chat_id)
    if not chat:
        raise HTTPException(status_code=404, detail="Chat not found")

    # For now, just return success without actual unarchiving logic
    # TODO: Implement proper unarchiving using archived_chats table
    return {
        "message": "Chat unarchived successfully",
        "chat": {
            "id": chat.id,
            "title": chat.title,
            "created_at": chat.created_at,
            "updated_at": chat.updated_at,
            "archived": False
        }
    }

@app.get("/chats/archived")
def get_archived_chats(db: Session = Depends(get_db)):
    """Get all archived chats"""
    # Get or create default user for demo purposes
    default_user_id = get_or_create_default_user(db)

    # For now, return empty list since archiving is not fully implemented
    # TODO: Implement proper archived chats retrieval using archived_chats table
    return []

@app.get("/chats/{chat_id}/messages")
def get_messages(chat_id: int, db: Session = Depends(get_db)):
    """Get messages for a chat"""
    # Check if chat exists
    chat = chat_crud.get_chat_by_id(db, chat_id)
    if not chat:
        raise HTTPException(status_code=404, detail="Chat not found")

    # Get messages from database
    messages = message_crud.get_chat_messages(db, chat_id)

    return [{"id": msg.id, "role": msg.role, "content": msg.content,
             "created_at": msg.created_at} for msg in messages]

# Duplicate functions removed - now using SQLQueryProcessor class methods

@app.post("/chats/{chat_id}/messages")
def send_message(chat_id: int, message_data: MessageCreate, request: Request, db: Session = Depends(get_db)):
    """Send message and get AI response"""
    # Apply rate limiting (50 messages per hour)
    check_rate_limit(request, max_requests=50, window_seconds=3600)

    # Check if chat exists
    chat = chat_crud.get_chat_by_id(db, chat_id)
    if not chat:
        raise HTTPException(status_code=404, detail="Chat not found")

    try:
        # Add user message to database
        user_message = message_crud.create_message(
            db=db,
            chat_id=chat_id,
            role="user",
            content=message_data.message
        )

        # Process the query using centralized SQL processor
        query_result = SQLQueryProcessor.process_natural_language_query(
            query=message_data.message,
            context="chat"
        )

        sql_query = query_result["sql_query"]
        assistant_content = query_result["content"]

        # Add assistant response to database
        assistant_message = message_crud.create_message(
            db=db,
            chat_id=chat_id,
            role="assistant",
            content=assistant_content,
            sql_query=sql_query
        )

        return {
            "messages": [
                {"id": user_message.id, "role": user_message.role, "content": user_message.content, "created_at": user_message.created_at},
                {"id": assistant_message.id, "role": assistant_message.role, "content": assistant_message.content, "created_at": assistant_message.created_at}
            ],
            "sql_query": sql_query
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Database Integration Endpoints
@app.get("/database/tables")
def get_tables(db: Session = Depends(get_db)):
    """Get all available database tables from the actual application database"""
    try:
        from sqlalchemy import inspect

        # Get the database inspector
        inspector = inspect(db.bind)

        # Get all table names
        all_table_names = inspector.get_table_names()

        # Filter out unwanted tables (legacy or system tables)
        excluded_tables = {'users', 'alembic_version'}  # Add tables to exclude here
        table_names = [name for name in all_table_names if name not in excluded_tables]

        tables = []
        for table_name in table_names:
            try:
                # Get column information for each table
                columns_info = inspector.get_columns(table_name)

                def simplify_column_type(col_type_str):
                    """Simplify column type for better readability"""
                    col_type_str = str(col_type_str).upper()

                    if 'VARCHAR' in col_type_str or 'TEXT' in col_type_str or 'CHAR' in col_type_str:
                        return 'string'
                    elif 'INTEGER' in col_type_str or 'INT' in col_type_str:
                        return 'int'
                    elif 'DECIMAL' in col_type_str or 'NUMERIC' in col_type_str or 'FLOAT' in col_type_str:
                        return 'decimal'
                    elif 'BOOLEAN' in col_type_str or 'BOOL' in col_type_str:
                        return 'boolean'
                    elif 'DATETIME' in col_type_str or 'TIMESTAMP' in col_type_str:
                        return 'datetime'
                    elif 'DATE' in col_type_str:
                        return 'date'
                    elif 'JSON' in col_type_str:
                        return 'json'
                    else:
                        return col_type_str.lower()

                columns = []
                for col in columns_info:
                    columns.append({
                        "name": col['name'],
                        "type": simplify_column_type(col['type']),
                        "raw_type": str(col['type']),  # Keep original for detailed view
                        "nullable": col.get('nullable', True),
                        "primary_key": col.get('primary_key', False)
                    })

                tables.append({
                    "name": table_name,
                    "columns": columns
                })
            except Exception as e:
                # If column info fails, add basic structure
                tables.append({
                    "name": table_name,
                    "columns": [{"name": "id", "type": "INTEGER", "nullable": False, "primary_key": True}]
                })

        return tables
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get tables: {str(e)}")

@app.get("/database/tables/{table_name}/schema")
def get_table_schema_endpoint(table_name: str, db: Session = Depends(get_db)):
    """Get schema for a specific table"""
    try:
        from sqlalchemy import inspect

        # Get the database inspector
        inspector = inspect(db.bind)

        # Check if table exists
        table_names = inspector.get_table_names()
        if table_name not in table_names:
            raise HTTPException(status_code=404, detail=f"Table '{table_name}' not found")

        # Get column information
        columns_info = inspector.get_columns(table_name)

        columns = []
        for col in columns_info:
            columns.append({
                "name": col['name'],
                "type": str(col['type']),
                "nullable": col.get('nullable', True),
                "primary_key": col.get('primary_key', False),
                "default": col.get('default', None)
            })

        # Get foreign keys
        foreign_keys = inspector.get_foreign_keys(table_name)

        # Get indexes
        indexes = inspector.get_indexes(table_name)

        return {
            "table_name": table_name,
            "columns": columns,
            "foreign_keys": foreign_keys,
            "indexes": indexes
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get table schema: {str(e)}")

@app.post("/database/query")
def execute_database_query(query_request: SecureDatabaseQuery):
    """Execute natural language query and return SQL + results"""
    # Process the query using centralized SQL processor
    query_result = SQLQueryProcessor.process_natural_language_query(
        query=query_request.query,
        context="direct"
    )

    if not query_result["success"]:
        raise HTTPException(status_code=500, detail=query_result.get("error", "Unknown error"))

    return {
        "sql_query": query_result["sql_query"],
        "natural_language_query": query_request.query,
        "selected_tables": query_request.selected_tables,
        "message": query_result["content"]
    }

# AI-Powered Endpoints (replaces hardcoded message handling)
@app.post("/ai/classify-intent")
async def classify_message_intent(request: dict):
    """AI-powered message intent classification endpoint."""
    try:
        message = request.get("message", "")
        context = request.get("context", {})

        if not message:
            raise HTTPException(status_code=400, detail="Message is required")

        # Use AI processor instead of hardcoded patterns
        result = ai_processor.classify_message_intent(message, context)

        return {
            "success": result.get("success", True),
            "intent": result.get("intent", "GENERAL_CONVERSATION"),
            "confidence": result.get("confidence", 0.5),
            "reasoning": result.get("reasoning", ""),
            "requires_sql": result.get("requires_sql", False),
            "is_database_related": result.get("is_database_related", False),
            "suggested_response_type": result.get("suggested_response_type", "conversational")
        }

    except Exception as e:
        logger.error(f"Error in AI intent classification: {e}")
        raise HTTPException(status_code=500, detail=f"Classification failed: {str(e)}")

@app.post("/ai/generate-placeholder")
async def generate_dynamic_placeholder(request: dict):
    """Generate dynamic placeholder text using AI."""
    try:
        context = request.get("context", {})

        # Use AI to generate contextual placeholder
        placeholder_prompt = f"""
        Generate a helpful, engaging placeholder text for a database assistant chat input.

        Context: {context}

        Requirements:
        - Keep it short (under 50 characters)
        - Make it relevant to database queries
        - Be encouraging and helpful
        - Consider time of day and context

        Examples: "Ask about your data...", "What would you like to know?", "Query your database..."

        Generate one placeholder text:
        """

        response = ai_processor.openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "Generate helpful, concise placeholder text for database queries."},
                {"role": "user", "content": placeholder_prompt}
            ],
            temperature=0.7,
            max_tokens=50
        )

        placeholder = response.choices[0].message.content.strip().replace('"', '')

        return {"placeholder": placeholder}

    except Exception as e:
        logger.error(f"Error generating placeholder: {e}")
        return {"placeholder": "Ask me about your data..."}

@app.post("/ai/generate-empty-state")
async def generate_dynamic_empty_state(request: dict):
    """Generate dynamic empty state content using AI."""
    try:
        context = request.get("context", {})

        empty_state_prompt = f"""
        Generate engaging empty state content for a database assistant chat interface.

        Context: {context}

        Generate a JSON response with:
        {{
            "title": "Engaging title (under 30 chars)",
            "description": "Helpful description (under 80 chars)",
            "suggestions": ["Query example 1", "Query example 2", "Query example 3"]
        }}

        Make it relevant to database queries and encouraging for users to start chatting.
        """

        response = ai_processor.openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "Generate helpful UI content for database applications. Always respond with valid JSON."},
                {"role": "user", "content": empty_state_prompt}
            ],
            temperature=0.7,
            max_tokens=200
        )

        ai_response = response.choices[0].message.content.strip()

        # Clean up response if it has markdown formatting
        if ai_response.startswith("```json"):
            ai_response = ai_response.replace("```json", "").replace("```", "").strip()

        result = json.loads(ai_response)
        return result

    except Exception as e:
        logger.error(f"Error generating empty state: {e}")
        return {
            "title": "Start exploring your data",
            "description": "Ask me anything about your database!",
            "suggestions": ["Show me all users", "Count total orders", "List recent data"]
        }

# ============================================================================
# HEALTH CHECK ENDPOINTS
# ============================================================================

@app.get("/")
def health_check():
    """Basic health check endpoint."""
    return {"status": "healthy", "message": "My SaaS Backend is running"}

@app.get("/health")
def detailed_health_check():
    """Detailed health check for monitoring systems."""
    try:
        from production_config import HealthChecker

        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "environment": os.getenv("APP_ENV", "development")
        }

        # Check database health
        db_health = HealthChecker.check_database_health()
        health_status.update(db_health)

        # Check dependencies
        deps_health = HealthChecker.check_dependencies()
        health_status["dependencies"] = deps_health

        # Determine overall status
        env = os.getenv("APP_ENV", "development").lower()

        # In development, be more lenient with health checks
        if env == "development":
            # Only fail if database is completely broken
            db_status = db_health.get("database", {}).get("status", "error")
            if db_status == "error":
                health_status["status"] = "degraded"  # Not completely unhealthy
            else:
                health_status["status"] = "healthy"
        else:
            # In production, be strict about all checks
            all_checks = [db_health.get("database", {}).get("status", "error")]
            all_checks.extend([dep.get("status", "error") for dep in deps_health.values()])

            if "error" in all_checks:
                health_status["status"] = "unhealthy"
                return JSONResponse(
                    status_code=503,
                    content=health_status
                )

        return health_status

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@app.get("/health/ready")
def readiness_check():
    """Kubernetes readiness probe endpoint."""
    try:
        # Check if application is ready to serve requests
        from mysql_manager import get_mysql_manager

        # Test database connection
        manager = get_mysql_manager()
        db_test = manager.test_connection()

        if db_test["status"] == "success":
            return {"status": "ready", "timestamp": datetime.now().isoformat()}
        else:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "not_ready",
                    "reason": "database_unavailable",
                    "timestamp": datetime.now().isoformat()
                }
            )

    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "not_ready",
                "reason": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@app.get("/health/live")
def liveness_check():
    """Kubernetes liveness probe endpoint."""
    # Simple liveness check - if this endpoint responds, the app is alive
    return {
        "status": "alive",
        "timestamp": datetime.now().isoformat(),
        "uptime": "unknown"  # Could be enhanced to track actual uptime
    }

# ============================================================================
# AUTHENTICATION ENDPOINTS
# ============================================================================

@app.post("/auth/register", response_model=Token)
async def register_user(user_data: SecureUserRegistration, request: Request, db: Session = Depends(get_db)):
    """Register a new user with JWT authentication."""
    try:
        # Rate limiting check
        client_ip = request.client.host if request.client else "unknown"
        if not auth_rate_limiter.is_allowed(f"register:{client_ip}", max_attempts=3, window_minutes=60):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Too many registration attempts. Please try again later."
            )

        # Validate email format
        if not validate_email_format(user_data.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid email format"
            )

        # Validate password strength
        password_validation = PasswordManager.validate_password_strength(user_data.password)
        if not password_validation["is_valid"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"message": "Password does not meet requirements", "errors": password_validation["errors"]}
            )

        # Check if user already exists
        existing_user = user_crud.get_user_by_email(db, user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        # Create new user
        new_user = user_crud.create_user(
            db=db,
            email=user_data.email,
            password=user_data.password,
            full_name=user_data.full_name
        )

        # Create JWT tokens
        token_data = {
            "sub": new_user.id,
            "email": new_user.email,
            "full_name": new_user.full_name
        }

        tokens = JWTManager.create_token_pair(token_data)

        # Reset rate limiting on successful registration
        auth_rate_limiter.reset_attempts(f"register:{client_ip}")

        logger.info(f"New user registered: {user_data.email}")
        return tokens

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )

@app.post("/auth/login", response_model=Token)
async def login_user(user_credentials: SecureUserLogin, request: Request, db: Session = Depends(get_db)):
    """Authenticate user and return JWT tokens."""
    try:
        # Rate limiting check
        client_ip = request.client.host if request.client else "unknown"
        if not auth_rate_limiter.is_allowed(f"login:{client_ip}", max_attempts=5, window_minutes=15):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Too many login attempts. Please try again later."
            )

        # Get user from database
        user = user_crud.get_user_by_email(db, user_credentials.email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )

        # Verify password
        if not PasswordManager.verify_password(user_credentials.password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )

        # Check if user is active
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Account is disabled"
            )

        # Create JWT tokens
        token_data = {
            "sub": user.id,
            "email": user.email,
            "full_name": user.full_name,
            "is_superuser": user.is_superuser
        }

        tokens = JWTManager.create_token_pair(token_data)

        # Update last login time
        from datetime import datetime
        user.last_login = datetime.now()
        db.commit()

        # Reset rate limiting on successful login
        auth_rate_limiter.reset_attempts(f"login:{client_ip}")

        logger.info(f"User logged in: {user_credentials.email}")
        return tokens

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@app.post("/auth/refresh", response_model=Token)
async def refresh_token(refresh_token: str, db: Session = Depends(get_db)):
    """Refresh access token using refresh token."""
    try:
        # Verify refresh token
        payload = JWTManager.verify_token(refresh_token, "refresh")

        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )

        # Get user from database
        user = user_crud.get_user_by_id(db, user_id)
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive"
            )

        # Create new token pair
        token_data = {
            "sub": user.id,
            "email": user.email,
            "full_name": user.full_name,
            "is_superuser": user.is_superuser
        }

        tokens = JWTManager.create_token_pair(token_data)

        logger.info(f"Token refreshed for user: {user.email}")
        return tokens

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )

@app.get("/auth/me")
async def get_current_user_info(current_user: dict = Depends(get_current_user), db: Session = Depends(get_db)):
    """Get current authenticated user information."""
    try:
        user_id = current_user.get("sub")
        user = user_crud.get_user_by_id(db, user_id)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        return {
            "id": user.id,
            "email": user.email,
            "full_name": user.full_name,
            "is_active": user.is_active,
            "is_superuser": user.is_superuser,
            "created_at": user.created_at,
            "last_login": user.last_login
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get user info error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user information"
        )

# ============================================================================
# MYSQL DATABASE MANAGEMENT ENDPOINTS
# ============================================================================

@app.get("/database/mysql/status")
async def get_mysql_status(current_user: dict = Depends(get_current_user)):
    """Get MySQL database status and connection information."""
    try:
        # Only allow superusers to access database management
        if not current_user.get("is_superuser", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )

        manager = get_mysql_manager()
        connection_test = manager.test_connection()
        database_info = manager.get_database_info()

        return {
            "connection": connection_test,
            "database_info": database_info,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"MySQL status check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get MySQL status"
        )

@app.post("/database/mysql/optimize")
async def optimize_mysql_database(current_user: dict = Depends(get_current_user)):
    """Optimize MySQL database tables for better performance."""
    try:
        # Only allow superusers to optimize database
        if not current_user.get("is_superuser", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )

        manager = get_mysql_manager()
        optimization_result = manager.optimize_database()

        if optimization_result["status"] == "success":
            logger.info(f"Database optimization completed by user {current_user.get('email')}")

        return optimization_result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Database optimization failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database optimization failed"
        )

@app.post("/database/mysql/backup")
async def create_mysql_backup(
    backup_name: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """Create a backup of the MySQL database."""
    try:
        # Only allow superusers to create backups
        if not current_user.get("is_superuser", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )

        manager = get_mysql_manager()

        # Generate backup filename if not provided
        if not backup_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}.sql"

        backup_result = manager.create_backup(backup_name)

        if backup_result["status"] == "success":
            logger.info(f"Database backup created by user {current_user.get('email')}: {backup_name}")

        return backup_result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Database backup failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database backup failed"
        )

@app.get("/database/mysql/health")
async def mysql_health_check():
    """Public endpoint for MySQL health monitoring (no auth required)."""
    try:
        manager = get_mysql_manager()
        connection_test = manager.test_connection()

        # Return minimal health info for monitoring
        return {
            "status": connection_test["status"],
            "mysql_version": connection_test.get("mysql_version", "unknown"),
            "timestamp": connection_test["timestamp"]
        }

    except Exception as e:
        logger.error(f"MySQL health check failed: {e}")
        return {
            "status": "error",
            "error": "Database connection failed",
            "timestamp": datetime.now().isoformat()
        }

# Original endpoint (keeping for backward compatibility)
@app.post("/query-text-to-sql/")
def query_text_to_sql(payload: QueryRequest):
    try:
        sql = run_nl_to_sql(payload.db_uri, payload.question)
        return {"sql_query": sql}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# TENANT MANAGEMENT ENDPOINTS
# ============================================================================

@app.get("/tenants", response_model=List[dict])
def get_tenants(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """Get all tenants with pagination"""
    try:
        tenant_crud = get_tenant_crud()
        tenants = tenant_crud.get_tenants(db, skip=skip, limit=limit)
        return [
            {
                "id": tenant.id,
                "employer_name": tenant.employer_name,
                "email": tenant.email,
                "phone": tenant.phone,
                "size": tenant.size,
                "created_at": tenant.created_at.isoformat() if tenant.created_at else None,
                "updated_at": tenant.updated_at.isoformat() if tenant.updated_at else None,
            }
            for tenant in tenants
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch tenants: {str(e)}")

@app.post("/tenants", response_model=dict)
def create_tenant(tenant_data: TenantCreate, db: Session = Depends(get_db)):
    """Create a new tenant"""
    try:
        tenant_crud = get_tenant_crud()

        # Check if tenant with email already exists
        existing_tenant = tenant_crud.get_tenant_by_email(db, tenant_data.email)
        if existing_tenant:
            raise HTTPException(status_code=400, detail="Tenant with this email already exists")

        tenant = tenant_crud.create_tenant(
            db=db,
            employer_name=tenant_data.employer_name,
            email=tenant_data.email,
            phone=tenant_data.phone,
            size=tenant_data.size
        )

        return {
            "id": tenant.id,
            "employer_name": tenant.employer_name,
            "email": tenant.email,
            "phone": tenant.phone,
            "size": tenant.size,
            "created_at": tenant.created_at.isoformat() if tenant.created_at else None,
            "updated_at": tenant.updated_at.isoformat() if tenant.updated_at else None,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create tenant: {str(e)}")

@app.get("/tenants/{tenant_id}", response_model=dict)
def get_tenant(tenant_id: int, db: Session = Depends(get_db)):
    """Get a specific tenant by ID"""
    try:
        tenant_crud = get_tenant_crud()
        tenant = tenant_crud.get_tenant_by_id(db, tenant_id)

        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        return {
            "id": tenant.id,
            "employer_name": tenant.employer_name,
            "email": tenant.email,
            "phone": tenant.phone,
            "size": tenant.size,
            "created_at": tenant.created_at.isoformat() if tenant.created_at else None,
            "updated_at": tenant.updated_at.isoformat() if tenant.updated_at else None,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch tenant: {str(e)}")

@app.put("/tenants/{tenant_id}", response_model=dict)
def update_tenant(tenant_id: int, tenant_data: TenantUpdate, db: Session = Depends(get_db)):
    """Update a tenant"""
    try:
        tenant_crud = get_tenant_crud()

        # Check if tenant exists
        existing_tenant = tenant_crud.get_tenant_by_id(db, tenant_id)
        if not existing_tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        # Check if email is being changed and if new email already exists
        if tenant_data.email and tenant_data.email != existing_tenant.email:
            email_exists = tenant_crud.get_tenant_by_email(db, tenant_data.email)
            if email_exists:
                raise HTTPException(status_code=400, detail="Tenant with this email already exists")

        # Update tenant
        update_data = {k: v for k, v in tenant_data.model_dump().items() if v is not None}
        tenant = tenant_crud.update_tenant(db, tenant_id, **update_data)

        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        return {
            "id": tenant.id,
            "employer_name": tenant.employer_name,
            "email": tenant.email,
            "phone": tenant.phone,
            "size": tenant.size,
            "created_at": tenant.created_at.isoformat() if tenant.created_at else None,
            "updated_at": tenant.updated_at.isoformat() if tenant.updated_at else None,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update tenant: {str(e)}")

@app.delete("/tenants/{tenant_id}")
def delete_tenant(tenant_id: int, db: Session = Depends(get_db)):
    """Delete a tenant"""
    try:
        tenant_crud = get_tenant_crud()

        # Check if tenant exists
        tenant = tenant_crud.get_tenant_by_id(db, tenant_id)
        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant not found")

        # Delete tenant
        success = tenant_crud.delete_tenant(db, tenant_id)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to delete tenant")

        return {"message": "Tenant deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete tenant: {str(e)}")

# ============================================================================
# TENANT USER MANAGEMENT ENDPOINTS
# ============================================================================

@app.get("/tenant-users", response_model=List[dict])
def get_tenant_users(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """Get all tenant users with pagination"""
    try:
        tenant_user_crud = get_tenant_user_crud()
        users = tenant_user_crud.get_tenant_users(db, skip=skip, limit=limit)
        return [
            {
                "id": user.id,
                "name": user.name,
                "email": user.email,
                "phone": user.phone,
                "role": user.role,
                "tenant_id": user.tenant_id,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
            }
            for user in users
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch tenant users: {str(e)}")

@app.get("/tenants/{tenant_id}/users", response_model=List[dict])
def get_tenant_users_by_tenant(tenant_id: int, db: Session = Depends(get_db)):
    """Get all users for a specific tenant"""
    try:
        tenant_user_crud = get_tenant_user_crud()
        users = tenant_user_crud.get_tenant_users_by_tenant(db, tenant_id)
        return [
            {
                "id": user.id,
                "name": user.name,
                "email": user.email,
                "phone": user.phone,
                "role": user.role,
                "tenant_id": user.tenant_id,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
            }
            for user in users
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch tenant users: {str(e)}")

@app.post("/tenant-users", response_model=dict)
def create_tenant_user(user_data: TenantUserCreate, db: Session = Depends(get_db)):
    """Create a new tenant user"""
    try:
        tenant_user_crud = get_tenant_user_crud()
        tenant_crud = get_tenant_crud()

        # Check if tenant exists
        tenant = tenant_crud.get_tenant_by_id(db, user_data.tenant_id)
        if not tenant:
            raise HTTPException(status_code=400, detail="Tenant not found")

        # Check if user with email already exists
        existing_user = tenant_user_crud.get_tenant_user_by_email(db, user_data.email)
        if existing_user:
            raise HTTPException(status_code=400, detail="User with this email already exists")

        user = tenant_user_crud.create_tenant_user(
            db=db,
            name=user_data.name,
            email=user_data.email,
            phone=user_data.phone,
            role=user_data.role,
            tenant_id=user_data.tenant_id
        )

        return {
            "id": user.id,
            "name": user.name,
            "email": user.email,
            "phone": user.phone,
            "role": user.role,
            "tenant_id": user.tenant_id,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create tenant user: {str(e)}")

@app.get("/tenant-users/{user_id}", response_model=dict)
def get_tenant_user(user_id: int, db: Session = Depends(get_db)):
    """Get a specific tenant user by ID"""
    try:
        tenant_user_crud = get_tenant_user_crud()
        user = tenant_user_crud.get_tenant_user_by_id(db, user_id)

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return {
            "id": user.id,
            "name": user.name,
            "email": user.email,
            "phone": user.phone,
            "role": user.role,
            "tenant_id": user.tenant_id,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch tenant user: {str(e)}")

@app.put("/tenant-users/{user_id}", response_model=dict)
def update_tenant_user(user_id: int, user_data: TenantUserUpdate, db: Session = Depends(get_db)):
    """Update a tenant user"""
    try:
        tenant_user_crud = get_tenant_user_crud()
        tenant_crud = get_tenant_crud()

        # Check if user exists
        existing_user = tenant_user_crud.get_tenant_user_by_id(db, user_id)
        if not existing_user:
            raise HTTPException(status_code=404, detail="User not found")

        # Check if tenant exists (if tenant_id is being updated)
        if user_data.tenant_id and user_data.tenant_id != existing_user.tenant_id:
            tenant = tenant_crud.get_tenant_by_id(db, user_data.tenant_id)
            if not tenant:
                raise HTTPException(status_code=400, detail="Tenant not found")

        # Check if email is being changed and if new email already exists
        if user_data.email and user_data.email != existing_user.email:
            email_exists = tenant_user_crud.get_tenant_user_by_email(db, user_data.email)
            if email_exists:
                raise HTTPException(status_code=400, detail="User with this email already exists")

        # Update user
        update_data = {k: v for k, v in user_data.model_dump().items() if v is not None}
        user = tenant_user_crud.update_tenant_user(db, user_id, **update_data)

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return {
            "id": user.id,
            "name": user.name,
            "email": user.email,
            "phone": user.phone,
            "role": user.role,
            "tenant_id": user.tenant_id,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update tenant user: {str(e)}")

@app.delete("/tenant-users/{user_id}")
def delete_tenant_user(user_id: int, db: Session = Depends(get_db)):
    """Delete a tenant user"""
    try:
        tenant_user_crud = get_tenant_user_crud()

        # Check if user exists
        user = tenant_user_crud.get_tenant_user_by_id(db, user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Delete user
        success = tenant_user_crud.delete_tenant_user(db, user_id)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to delete user")

        return {"message": "User deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete tenant user: {str(e)}")

# ============================================================================
# ADMIN DASHBOARD ROUTES (HTML)
# ============================================================================

@app.get("/admin", response_class=HTMLResponse)
async def admin_dashboard(request: Request, db: Session = Depends(get_db), admin_user: str = Depends(verify_admin_credentials)):
    """Admin dashboard home page"""
    try:
        crud_instances = get_crud_instances()
        tenant_crud = crud_instances['tenant_crud']
        tenant_user_crud = crud_instances['tenant_user_crud']

        # Get stats
        tenants = tenant_crud.get_tenants(db)
        users = tenant_user_crud.get_tenant_users(db)

        # Get chat and message stats
        from crud import ChatCRUD, MessageCRUD

        chats = ChatCRUD.get_all_chats(db)
        messages = MessageCRUD.get_all_messages(db)

        stats = {
            'total_tenants': len(tenants),
            'total_users': len(users),
            'active_users': len([u for u in users if u.role in ['admin', 'analyst']]),
            'admin_users': len([u for u in users if u.role == 'admin']),
            'total_chats': len(chats),
            'total_messages': len(messages),
            'active_chats': len([c for c in chats if not hasattr(c, 'is_archived') or not c.is_archived])
        }

        # Get recent tenants (last 5)
        recent_tenants = sorted(tenants, key=lambda x: x.created_at or datetime.min, reverse=True)[:5]

        return templates.TemplateResponse("dashboard.html", {
            "request": request,
            "stats": stats,
            "recent_tenants": recent_tenants,
            "current_time": datetime.now()
        })
    except Exception as e:
        return templates.TemplateResponse("dashboard.html", {
            "request": request,
            "stats": {'total_tenants': 0, 'total_users': 0, 'active_users': 0, 'admin_users': 0},
            "recent_tenants": [],
            "current_time": datetime.now(),
            "messages": [("error", f"Error loading dashboard: {str(e)}")]
        })

@app.get("/admin/tenants", response_class=HTMLResponse)
async def admin_tenants_list(request: Request, db: Session = Depends(get_db), admin_user: str = Depends(verify_admin_credentials)):
    """Admin tenants list page"""
    try:
        tenant_crud = get_crud_instances()['tenant_crud']
        tenant_user_crud = get_tenant_user_crud()

        # Get search and filter parameters
        search = request.query_params.get('search', '').strip()
        size_filter = request.query_params.get('size', '').strip()

        # Get all tenants
        tenants = tenant_crud.get_tenants(db)

        # Apply filters
        if search:
            tenants = [t for t in tenants if
                      search.lower() in t.employer_name.lower() or
                      search.lower() in t.email.lower()]

        if size_filter:
            tenants = [t for t in tenants if t.size == size_filter]

        # Add user count for each tenant
        for tenant in tenants:
            tenant.user_count = len(tenant_user_crud.get_tenant_users_by_tenant(db, tenant.id))

        return templates.TemplateResponse("tenants.html", {
            "request": request,
            "tenants": tenants
        })
    except Exception as e:
        return templates.TemplateResponse("tenants.html", {
            "request": request,
            "tenants": [],
            "messages": [("error", f"Error loading tenants: {str(e)}")]
        })

@app.get("/admin/tenants/new", response_class=HTMLResponse)
async def admin_tenant_create_form(request: Request, admin_user: str = Depends(verify_admin_credentials)):
    """Admin create tenant form"""
    return templates.TemplateResponse("tenant_form.html", {
        "request": request,
        "tenant": None,
        "form_data": {},
        "errors": {}
    })

@app.post("/admin/tenants/new", response_class=HTMLResponse)
async def admin_tenant_create(
    request: Request,
    db: Session = Depends(get_db),
    admin_user: str = Depends(verify_admin_credentials),
    employer_name: str = Form(...),
    email: str = Form(...),
    phone: str = Form(""),
    size: str = Form("")
):
    """Admin create tenant"""
    form_data = {
        'employer_name': employer_name,
        'email': email,
        'phone': phone,
        'size': size
    }

    # Get validation and creation functions
    validate_func, create_func = AdminFormProcessor.create_tenant_validation_and_creation()

    # Use generic form processor
    return AdminFormProcessor.process_admin_form(
        request=request,
        db=db,
        template_name="tenant_form.html",
        form_data=form_data,
        validation_func=validate_func,
        creation_func=create_func,
        success_redirect="/admin/tenants?created=1",
        context_data={"tenant": None}
    )

@app.get("/admin/tenants/{tenant_id}", response_class=HTMLResponse)
async def admin_tenant_detail(request: Request, tenant_id: int, db: Session = Depends(get_db)):
    """Admin tenant detail/edit form"""
    try:
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()

        tenant = tenant_crud.get_tenant_by_id(db, tenant_id)
        if not tenant:
            return templates.TemplateResponse("tenant_form.html", {
                "request": request,
                "tenant": None,
                "form_data": {},
                "errors": {"general": "Tenant not found"}
            })

        # Get tenant users
        tenant_users = tenant_user_crud.get_tenant_users_by_tenant(db, tenant_id)

        messages = []
        if request.query_params.get('created'):
            messages.append(("success", "Tenant created successfully!"))
        if request.query_params.get('updated'):
            messages.append(("success", "Tenant updated successfully!"))

        return templates.TemplateResponse("tenant_form.html", {
            "request": request,
            "tenant": tenant,
            "tenant_users": tenant_users,
            "form_data": {},
            "errors": {},
            "messages": messages
        })
    except Exception as e:
        return templates.TemplateResponse("tenant_form.html", {
            "request": request,
            "tenant": None,
            "form_data": {},
            "errors": {"general": f"Error loading tenant: {str(e)}"}
        })

@app.get("/admin/tenants/{tenant_id}/edit", response_class=HTMLResponse)
async def admin_tenant_edit_form(request: Request, tenant_id: int, db: Session = Depends(get_db)):
    """Admin edit tenant form"""
    return await admin_tenant_detail(request, tenant_id, db)

@app.post("/admin/tenants/{tenant_id}/edit", response_class=HTMLResponse)
async def admin_tenant_update(
    request: Request,
    tenant_id: int,
    db: Session = Depends(get_db),
    employer_name: str = Form(...),
    email: str = Form(...),
    phone: str = Form(""),
    size: str = Form("")
):
    """Admin update tenant"""
    form_data = {
        'employer_name': employer_name,
        'email': email,
        'phone': phone,
        'size': size
    }
    errors = {}

    try:
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()

        # Get existing tenant
        tenant = tenant_crud.get_tenant_by_id(db, tenant_id)
        if not tenant:
            return templates.TemplateResponse("tenant_form.html", {
                "request": request,
                "tenant": None,
                "form_data": form_data,
                "errors": {"general": "Tenant not found"}
            })

        # Validate
        if not employer_name.strip():
            errors['employer_name'] = 'Company name is required'
        if not email.strip():
            errors['email'] = 'Email is required'
        elif '@' not in email:
            errors['email'] = 'Invalid email format'

        # Check if email exists (but not for current tenant)
        if email != tenant.email:
            existing = tenant_crud.get_tenant_by_email(db, email)
            if existing:
                errors['email'] = 'A tenant with this email already exists'

        if errors:
            tenant_users = tenant_user_crud.get_tenant_users_by_tenant(db, tenant_id)
            return templates.TemplateResponse("tenant_form.html", {
                "request": request,
                "tenant": tenant,
                "tenant_users": tenant_users,
                "form_data": form_data,
                "errors": errors
            })

        # Update tenant
        update_data = {
            'employer_name': employer_name,
            'email': email,
            'phone': phone if phone else None,
            'size': size if size else None
        }

        tenant_crud.update_tenant(db, tenant_id, **update_data)

        return RedirectResponse(url=f"/admin/tenants/{tenant_id}?updated=1", status_code=303)

    except Exception as e:
        tenant_users = tenant_user_crud.get_tenant_users_by_tenant(db, tenant_id) if tenant else []
        return templates.TemplateResponse("tenant_form.html", {
            "request": request,
            "tenant": tenant,
            "tenant_users": tenant_users,
            "form_data": form_data,
            "errors": {"general": f"Error updating tenant: {str(e)}"}
        })

@app.post("/admin/tenants/{tenant_id}/delete")
async def admin_tenant_delete(tenant_id: int, db: Session = Depends(get_db)):
    """Admin delete tenant"""
    try:
        tenant_crud = get_tenant_crud()

        # Check if tenant exists
        tenant = tenant_crud.get_tenant_by_id(db, tenant_id)
        if not tenant:
            return RedirectResponse(url="/admin/tenants?error=Tenant not found", status_code=303)

        # Delete tenant
        success = tenant_crud.delete_tenant(db, tenant_id)
        if success:
            return RedirectResponse(url="/admin/tenants?deleted=1", status_code=303)
        else:
            return RedirectResponse(url="/admin/tenants?error=Failed to delete tenant", status_code=303)

    except Exception as e:
        return RedirectResponse(url=f"/admin/tenants?error=Error deleting tenant: {str(e)}", status_code=303)

# ============================================================================
# ADMIN USER MANAGEMENT ROUTES
# ============================================================================

@app.get("/admin/users", response_class=HTMLResponse)
async def admin_users_list(request: Request, db: Session = Depends(get_db), admin_user: str = Depends(verify_admin_credentials)):
    """Admin users list page"""
    try:
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()

        # Get search and filter parameters
        search = request.query_params.get('search', '').strip()
        tenant_filter = request.query_params.get('tenant_id', '').strip()
        role_filter = request.query_params.get('role', '').strip()

        # Get all users and tenants
        users = tenant_user_crud.get_tenant_users(db)
        all_tenants = tenant_crud.get_tenants(db)

        # Create tenant lookup
        tenant_lookup = {t.id: t.employer_name for t in all_tenants}

        # Apply filters
        if search:
            users = [u for u in users if
                    search.lower() in u.name.lower() or
                    search.lower() in u.email.lower() or
                    search.lower() in tenant_lookup.get(u.tenant_id, '').lower()]

        if tenant_filter:
            users = [u for u in users if u.tenant_id == tenant_filter]

        if role_filter:
            users = [u for u in users if u.role == role_filter]

        # Add tenant name to users
        for user in users:
            user.tenant_name = tenant_lookup.get(user.tenant_id, 'Unknown')

        messages = []
        if request.query_params.get('deleted'):
            messages.append(("success", "User deleted successfully!"))
        if request.query_params.get('error'):
            messages.append(("error", request.query_params.get('error')))

        return templates.TemplateResponse("users.html", {
            "request": request,
            "users": users,
            "all_tenants": all_tenants,
            "messages": messages
        })
    except Exception as e:
        return templates.TemplateResponse("users.html", {
            "request": request,
            "users": [],
            "all_tenants": [],
            "messages": [("error", f"Error loading users: {str(e)}")]
        })

@app.get("/admin/users/new", response_class=HTMLResponse)
async def admin_user_create_form(request: Request, db: Session = Depends(get_db)):
    """Admin create user form"""
    try:
        tenant_crud = get_tenant_crud()
        tenants = tenant_crud.get_tenants(db)

        return templates.TemplateResponse("user_form.html", {
            "request": request,
            "user": None,
            "tenants": tenants,
            "form_data": {},
            "errors": {}
        })
    except Exception as e:
        return templates.TemplateResponse("user_form.html", {
            "request": request,
            "user": None,
            "tenants": [],
            "form_data": {},
            "errors": {"general": f"Error loading form: {str(e)}"}
        })

@app.post("/admin/users/new", response_class=HTMLResponse)
async def admin_user_create(
    request: Request,
    db: Session = Depends(get_db),
    name: str = Form(...),
    email: str = Form(...),
    phone: str = Form(""),
    role: str = Form(...),
    tenant_id: int = Form(...)
):
    """Admin create user"""
    form_data = {
        'name': name,
        'email': email,
        'phone': phone,
        'role': role,
        'tenant_id': tenant_id
    }

    try:
        crud_instances = get_crud_instances()
        tenant_crud = crud_instances['tenant_crud']
        tenant_user_crud = crud_instances['tenant_user_crud']
        tenants = tenant_crud.get_tenants(db)

        # Validate form data
        errors = validate_user_form(name, email, role, tenant_id, phone)

        # Check if tenant exists
        if tenant_id:
            tenant = tenant_crud.get_tenant_by_id(db, tenant_id)
            if not tenant:
                errors['tenant_id'] = 'Selected company does not exist'

        # Check if email exists
        if email_error := check_email_exists(db, email, tenant_user_crud):
            errors['email'] = email_error

        if errors:
            return create_error_template_response("user_form.html", request, {
                "user": None,
                "tenants": tenants,
                "form_data": form_data
            }, errors)

        # Create user
        user = tenant_user_crud.create_tenant_user(
            db=db,
            name=name,
            email=email,
            phone=phone if phone else None,
            role=role,
            tenant_id=tenant_id
        )

        return RedirectResponse(url=f"/admin/users?created=1", status_code=303)

    except Exception as e:
        tenants = tenant_crud.get_tenants(db) if 'tenant_crud' in locals() else []
        return create_error_template_response("user_form.html", request, {
            "user": None,
            "tenants": tenants,
            "form_data": form_data
        }, {"general": f"Error creating user: {str(e)}"})

@app.get("/admin/users/{user_id}/edit", response_class=HTMLResponse)
async def admin_user_edit_form(request: Request, user_id: int, db: Session = Depends(get_db)):
    """Admin edit user form"""
    try:
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()

        user = tenant_user_crud.get_tenant_user_by_id(db, user_id)
        if not user:
            return templates.TemplateResponse("user_form.html", {
                "request": request,
                "user": None,
                "tenants": [],
                "form_data": {},
                "errors": {"general": "User not found"}
            })

        tenants = tenant_crud.get_tenants(db)

        messages = []
        if request.query_params.get('updated'):
            messages.append(("success", "User updated successfully!"))

        return templates.TemplateResponse("user_form.html", {
            "request": request,
            "user": user,
            "tenants": tenants,
            "form_data": {},
            "errors": {},
            "messages": messages
        })
    except Exception as e:
        return templates.TemplateResponse("user_form.html", {
            "request": request,
            "user": None,
            "tenants": [],
            "form_data": {},
            "errors": {"general": f"Error loading user: {str(e)}"}
        })

@app.post("/admin/users/{user_id}/edit", response_class=HTMLResponse)
async def admin_user_update(
    request: Request,
    user_id: int,
    db: Session = Depends(get_db),
    name: str = Form(...),
    email: str = Form(...),
    phone: str = Form(""),
    role: str = Form(...),
    tenant_id: int = Form(...)
):
    """Admin update user"""
    form_data = {
        'name': name,
        'email': email,
        'phone': phone,
        'role': role,
        'tenant_id': tenant_id
    }
    errors = {}

    try:
        tenant_crud = get_tenant_crud()
        tenant_user_crud = get_tenant_user_crud()

        # Get existing user
        user = tenant_user_crud.get_tenant_user_by_id(db, user_id)
        if not user:
            return templates.TemplateResponse("user_form.html", {
                "request": request,
                "user": None,
                "tenants": [],
                "form_data": form_data,
                "errors": {"general": "User not found"}
            })

        tenants = tenant_crud.get_tenants(db)

        # Validate
        if not name.strip():
            errors['name'] = 'Name is required'
        if not email.strip():
            errors['email'] = 'Email is required'
        elif '@' not in email:
            errors['email'] = 'Invalid email format'
        if not role:
            errors['role'] = 'Role is required'
        if not tenant_id:
            errors['tenant_id'] = 'Company is required'

        # Check if tenant exists
        if tenant_id:
            tenant = tenant_crud.get_tenant_by_id(db, tenant_id)
            if not tenant:
                errors['tenant_id'] = 'Selected company does not exist'

        # Check if email exists (but not for current user)
        if email != user.email:
            existing = tenant_user_crud.get_tenant_user_by_email(db, email)
            if existing:
                errors['email'] = 'A user with this email already exists'

        if errors:
            return templates.TemplateResponse("user_form.html", {
                "request": request,
                "user": user,
                "tenants": tenants,
                "form_data": form_data,
                "errors": errors
            })

        # Update user
        update_data = {
            'name': name,
            'email': email,
            'phone': phone if phone else None,
            'role': role,
            'tenant_id': tenant_id
        }

        tenant_user_crud.update_tenant_user(db, user_id, **update_data)

        return RedirectResponse(url=f"/admin/users/{user_id}/edit?updated=1", status_code=303)

    except Exception as e:
        tenants = tenant_crud.get_tenants(db) if 'tenant_crud' in locals() else []
        return templates.TemplateResponse("user_form.html", {
            "request": request,
            "user": user if 'user' in locals() else None,
            "tenants": tenants,
            "form_data": form_data,
            "errors": {"general": f"Error updating user: {str(e)}"}
        })

@app.post("/admin/users/{user_id}/delete")
async def admin_user_delete(user_id: int, db: Session = Depends(get_db)):
    """Admin delete user"""
    try:
        tenant_user_crud = get_tenant_user_crud()

        # Check if user exists
        user = tenant_user_crud.get_tenant_user_by_id(db, user_id)
        if not user:
            return RedirectResponse(url="/admin/users?error=User not found", status_code=303)

        # Delete user
        success = tenant_user_crud.delete_tenant_user(db, user_id)
        if success:
            return RedirectResponse(url="/admin/users?deleted=1", status_code=303)
        else:
            return RedirectResponse(url="/admin/users?error=Failed to delete user", status_code=303)

    except Exception as e:
        return RedirectResponse(url=f"/admin/users?error=Error deleting user: {str(e)}", status_code=303)


# Admin Users Management Routes
@app.get("/admin/admin-users", response_class=HTMLResponse)
async def admin_users_list(request: Request, db: Session = Depends(get_db)):
    """Display admin users list page."""
    admin_user_crud = get_admin_user_crud()

    # Get filter parameters
    search = request.query_params.get('search', '').strip()
    role_filter = request.query_params.get('role', '').strip()
    status_filter = request.query_params.get('status', '').strip()

    # Get all admin users
    admin_users = admin_user_crud.get_admin_users(db)

    # Apply filters
    if search:
        admin_users = [u for u in admin_users if
                      search.lower() in u.name.lower() or
                      search.lower() in u.email.lower()]

    if role_filter:
        admin_users = [u for u in admin_users if u.role == role_filter]

    if status_filter:
        admin_users = [u for u in admin_users if u.status == status_filter]

    return templates.TemplateResponse("admin_users.html", {
        "request": request,
        "admin_users": admin_users
    })


@app.get("/admin/admin-users/new", response_class=HTMLResponse)
async def admin_user_create_form(request: Request):
    """Display admin user creation form."""
    return templates.TemplateResponse("admin_user_form.html", {
        "request": request,
        "admin_user": None,
        "errors": {},
        "form_data": {}
    })


@app.post("/admin/admin-users/new", response_class=HTMLResponse)
async def admin_user_create(request: Request, db: Session = Depends(get_db)):
    """Handle admin user creation."""
    admin_user_crud = get_admin_user_crud()
    form_data = await request.form()

    # Extract form data
    name = form_data.get('name', '').strip()
    email = form_data.get('email', '').strip()
    phone = form_data.get('phone', '').strip() or None
    role = form_data.get('role', '').strip()
    password = form_data.get('password', '').strip()
    confirm_password = form_data.get('confirm_password', '').strip()
    status = form_data.get('status', 'active').strip()

    # Validate form data
    errors = validate_admin_user_form(name, email, role, password, confirm_password, is_update=False)

    # Check if email exists
    if email_error := check_email_exists(db, email, admin_user_crud):
        errors['email'] = email_error

    if errors:
        return create_error_template_response("admin_user_form.html", request, {
            "admin_user": None,
            "form_data": form_data
        }, errors)

    try:
        admin_user_crud.create_admin_user(
            db=db,
            name=name,
            email=email,
            password=password,
            role=role,
            phone=phone,
            status=status
        )
        return RedirectResponse(url="/admin/admin-users", status_code=303)
    except Exception as e:
        return create_error_template_response("admin_user_form.html", request, {
            "admin_user": None,
            "form_data": form_data
        }, {"general": f"Error creating admin user: {str(e)}"})


@app.get("/admin/admin-users/{user_id}/edit", response_class=HTMLResponse)
async def admin_user_edit_form(request: Request, user_id: int, db: Session = Depends(get_db)):
    """Display admin user edit form."""
    admin_user_crud = get_admin_user_crud()
    admin_user = admin_user_crud.get_admin_user_by_id(db, user_id)

    if not admin_user:
        return RedirectResponse(url="/admin/admin-users?error=Admin user not found", status_code=303)

    return templates.TemplateResponse("admin_user_form.html", {
        "request": request,
        "admin_user": admin_user,
        "errors": {},
        "form_data": {}
    })


@app.post("/admin/admin-users/{user_id}/edit", response_class=HTMLResponse)
async def admin_user_edit(request: Request, user_id: int, db: Session = Depends(get_db)):
    """Handle admin user edit."""
    admin_user_crud = get_admin_user_crud()
    form_data = await request.form()
    errors = {}

    # Get existing admin user
    admin_user = admin_user_crud.get_admin_user_by_id(db, user_id)
    if not admin_user:
        return RedirectResponse(url="/admin/admin-users?error=Admin user not found", status_code=303)

    # Validate form data
    name = form_data.get('name', '').strip()
    email = form_data.get('email', '').strip()
    phone = form_data.get('phone', '').strip() or None
    role = form_data.get('role', '').strip()
    status = form_data.get('status', 'active').strip()
    new_password = form_data.get('new_password', '').strip()
    confirm_new_password = form_data.get('confirm_new_password', '').strip()

    if not name:
        errors['name'] = 'Name is required'

    if not email:
        errors['email'] = 'Email is required'
    elif email != admin_user.email and admin_user_crud.get_admin_user_by_email(db, email):
        errors['email'] = 'Email already exists'

    if not role:
        errors['role'] = 'Role is required'

    # Password validation (only if new password is provided)
    if new_password:
        if len(new_password) < 8:
            errors['new_password'] = 'Password must be at least 8 characters'
        elif new_password != confirm_new_password:
            errors['confirm_new_password'] = 'Passwords do not match'

    if errors:
        return templates.TemplateResponse("admin_user_form.html", {
            "request": request,
            "admin_user": admin_user,
            "errors": errors,
            "form_data": form_data
        })

    try:
        # Update admin user
        update_data = {
            'name': name,
            'email': email,
            'phone': phone,
            'role': role,
            'status': status
        }

        admin_user_crud.update_admin_user(db, user_id, **update_data)

        # Update password if provided
        if new_password:
            admin_user_crud.update_password(db, user_id, new_password)

        return RedirectResponse(url=f"/admin/admin-users/{user_id}/edit?updated=1", status_code=303)
    except Exception as e:
        errors['general'] = f'Error updating admin user: {str(e)}'
        return templates.TemplateResponse("admin_user_form.html", {
            "request": request,
            "admin_user": admin_user,
            "errors": errors,
            "form_data": form_data
        })


@app.post("/admin/admin-users/{user_id}/activate")
async def admin_user_activate(user_id: int, db: Session = Depends(get_db)):
    """Activate an admin user."""
    try:
        admin_user_crud = get_admin_user_crud()
        admin_user = admin_user_crud.activate_admin_user(db, user_id)

        if admin_user:
            return RedirectResponse(url="/admin/admin-users?activated=1", status_code=303)
        else:
            return RedirectResponse(url="/admin/admin-users?error=Admin user not found", status_code=303)
    except Exception as e:
        return RedirectResponse(url=f"/admin/admin-users?error=Error activating admin user: {str(e)}", status_code=303)


@app.post("/admin/admin-users/{user_id}/deactivate")
async def admin_user_deactivate(user_id: int, db: Session = Depends(get_db)):
    """Deactivate an admin user."""
    try:
        admin_user_crud = get_admin_user_crud()
        admin_user = admin_user_crud.deactivate_admin_user(db, user_id)

        if admin_user:
            return RedirectResponse(url="/admin/admin-users?deactivated=1", status_code=303)
        else:
            return RedirectResponse(url="/admin/admin-users?error=Admin user not found", status_code=303)
    except Exception as e:
        return RedirectResponse(url=f"/admin/admin-users?error=Error deactivating admin user: {str(e)}", status_code=303)


@app.post("/admin/admin-users/{user_id}/delete")
async def admin_user_delete(user_id: int, db: Session = Depends(get_db)):
    """Delete an admin user."""
    try:
        admin_user_crud = get_admin_user_crud()

        # Check if admin user exists
        admin_user = admin_user_crud.get_admin_user_by_id(db, user_id)
        if not admin_user:
            return RedirectResponse(url="/admin/admin-users?error=Admin user not found", status_code=303)

        # Delete admin user
        success = admin_user_crud.delete_admin_user(db, user_id)
        if success:
            return RedirectResponse(url="/admin/admin-users?deleted=1", status_code=303)
        else:
            return RedirectResponse(url="/admin/admin-users?error=Failed to delete admin user", status_code=303)
    except Exception as e:
        return RedirectResponse(url=f"/admin/admin-users?error=Error deleting admin user: {str(e)}", status_code=303)


# ============================================================================
# ADMIN CHAT MANAGEMENT ROUTES
# ============================================================================

@app.get("/admin/chats", response_class=HTMLResponse)
async def admin_chats_list(request: Request, db: Session = Depends(get_db), admin_user: str = Depends(verify_admin_credentials)):
    """Admin chats list page"""
    try:
        chat_crud = get_chat_crud()
        tenant_user_crud = get_tenant_user_crud()

        # Get search and filter parameters
        search = request.query_params.get('search', '').strip()
        user_filter = request.query_params.get('user_id', '').strip()

        # Get all chats
        chats = chat_crud.get_all_chats(db)
        all_users = tenant_user_crud.get_tenant_users(db)

        # Create user lookup
        user_lookup = {u.id: f"{u.name} ({u.email})" for u in all_users}

        # Apply filters
        if search:
            chats = [c for c in chats if
                    search.lower() in c.title.lower() or
                    (c.description and search.lower() in c.description.lower())]

        if user_filter:
            try:
                user_id = int(user_filter)
                chats = [c for c in chats if c.tenant_user_id == user_id]
            except ValueError:
                pass

        # Add user info and message count for each chat
        message_crud = get_message_crud()
        for chat in chats:
            chat.user_name = user_lookup.get(chat.tenant_user_id, "Unknown User")
            chat.message_count = len(message_crud.get_chat_messages(db, chat.id))

        return templates.TemplateResponse("admin_chats.html", {
            "request": request,
            "chats": chats,
            "all_users": all_users
        })
    except Exception as e:
        return templates.TemplateResponse("admin_chats.html", {
            "request": request,
            "chats": [],
            "all_users": [],
            "messages": [("error", f"Error loading chats: {str(e)}")]
        })

@app.get("/admin/chats/{chat_id}", response_class=HTMLResponse)
async def admin_chat_detail(request: Request, chat_id: int, db: Session = Depends(get_db), admin_user: str = Depends(verify_admin_credentials)):
    """Admin chat detail page with messages"""
    try:
        chat_crud = get_chat_crud()
        message_crud = get_message_crud()
        tenant_user_crud = get_tenant_user_crud()

        chat = chat_crud.get_chat_by_id(db, chat_id)
        if not chat:
            return templates.TemplateResponse("admin_chat_detail.html", {
                "request": request,
                "chat": None,
                "messages": [("error", "Chat not found")]
            })

        # Get chat messages
        chat_messages = message_crud.get_chat_messages(db, chat_id)

        # Get user info
        user = tenant_user_crud.get_tenant_user_by_id(db, chat.tenant_user_id)
        chat.user_name = user.name if user else "Unknown User"
        chat.user_email = user.email if user else "Unknown"

        return templates.TemplateResponse("admin_chat_detail.html", {
            "request": request,
            "chat": chat,
            "chat_messages": chat_messages
        })
    except Exception as e:
        return templates.TemplateResponse("admin_chat_detail.html", {
            "request": request,
            "chat": None,
            "chat_messages": [],
            "messages": [("error", f"Error loading chat: {str(e)}")]
        })

@app.get("/admin/messages", response_class=HTMLResponse)
async def admin_messages_list(request: Request, db: Session = Depends(get_db), admin_user: str = Depends(verify_admin_credentials)):
    """Admin messages list page"""
    try:
        from crud import MessageCRUD, ChatCRUD
        tenant_user_crud = get_tenant_user_crud()

        # Get search and filter parameters
        search = request.query_params.get('search', '').strip()
        role_filter = request.query_params.get('role', '').strip()
        chat_filter = request.query_params.get('chat_id', '').strip()

        # Get all messages
        messages = MessageCRUD.get_all_messages(db)
        all_chats = ChatCRUD.get_all_chats(db)
        all_users = tenant_user_crud.get_tenant_users(db)

        # Create lookups
        chat_lookup = {c.id: c.title for c in all_chats}
        user_lookup = {u.id: f"{u.name} ({u.email})" for u in all_users}

        # Apply filters
        if search:
            messages = [m for m in messages if search.lower() in m.content.lower()]

        if role_filter:
            messages = [m for m in messages if m.role == role_filter]

        if chat_filter:
            try:
                chat_id = int(chat_filter)
                messages = [m for m in messages if m.chat_id == chat_id]
            except ValueError:
                pass

        # Add chat and user info for each message
        for message in messages:
            message.chat_title = chat_lookup.get(message.chat_id, "Unknown Chat")
            message.user_name = user_lookup.get(message.tenant_user_id, "System") if message.tenant_user_id else "System"

        return templates.TemplateResponse("admin_messages.html", {
            "request": request,
            "messages": messages,
            "all_chats": all_chats
        })
    except Exception as e:
        return templates.TemplateResponse("admin_messages.html", {
            "request": request,
            "messages": [],
            "all_chats": [],
            "messages": [("error", f"Error loading messages: {str(e)}")]
        })


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
