"""
MySQL Database Management Module

This module provides MySQL-specific database management functionality including:
- Database creation and initialization
- Connection health monitoring
- Performance optimization
- Backup and restore operations
- Migration management
"""

import os
import logging
import pymysql
from typing import Dict, List, Optional, Any
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime, timedelta
import subprocess
import json

logger = logging.getLogger(__name__)

class MySQLManager:
    """MySQL database management and operations."""
    
    def __init__(self, database_url: str):
        """Initialize MySQL manager with database URL."""
        self.database_url = database_url
        self.engine = None
        self._parse_connection_params()
    
    def _parse_connection_params(self):
        """Parse MySQL connection parameters from URL."""
        try:
            # Parse URL: mysql+pymysql://user:password@host:port/database
            if "mysql+pymysql://" in self.database_url:
                url_parts = self.database_url.replace("mysql+pymysql://", "").split("/")
                auth_host = url_parts[0]
                self.database_name = url_parts[1] if len(url_parts) > 1 else "mysql"
                
                if "@" in auth_host:
                    auth, host_port = auth_host.split("@")
                    if ":" in auth:
                        self.username, self.password = auth.split(":", 1)
                    else:
                        self.username = auth
                        self.password = ""
                else:
                    host_port = auth_host
                    self.username = "root"
                    self.password = ""
                
                if ":" in host_port:
                    self.host, port_str = host_port.split(":")
                    self.port = int(port_str)
                else:
                    self.host = host_port
                    self.port = 3306
                    
        except Exception as e:
            logger.error(f"Error parsing MySQL URL: {e}")
            # Set defaults
            self.host = "localhost"
            self.port = 3306
            self.username = "root"
            self.password = ""
            self.database_name = "saas_db"
    
    def create_database_if_not_exists(self) -> bool:
        """Create database if it doesn't exist."""
        try:
            # Connect without specifying database
            connection_url = f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/"
            temp_engine = create_engine(connection_url)
            
            with temp_engine.connect() as conn:
                # Check if database exists
                result = conn.execute(text(f"SHOW DATABASES LIKE '{self.database_name}'"))
                if not result.fetchone():
                    # Create database
                    conn.execute(text(f"CREATE DATABASE {self.database_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                    conn.commit()
                    logger.info(f"Created database: {self.database_name}")
                else:
                    logger.info(f"Database already exists: {self.database_name}")
            
            temp_engine.dispose()
            return True
            
        except Exception as e:
            logger.error(f"Error creating database: {e}")
            return False
    
    def test_connection(self) -> Dict[str, Any]:
        """Test MySQL connection and return status."""
        try:
            engine = create_engine(self.database_url)
            
            with engine.connect() as conn:
                # Test basic connection
                result = conn.execute(text("SELECT 1 as test"))
                test_value = result.fetchone()[0]
                
                # Get MySQL version
                version_result = conn.execute(text("SELECT VERSION() as version"))
                mysql_version = version_result.fetchone()[0]
                
                # Get connection info
                conn_info = conn.execute(text("SELECT CONNECTION_ID() as conn_id")).fetchone()[0]
                
                # Test database access
                db_result = conn.execute(text("SELECT DATABASE() as current_db"))
                current_db = db_result.fetchone()[0]
                
            engine.dispose()
            
            return {
                "status": "success",
                "mysql_version": mysql_version,
                "connection_id": conn_info,
                "current_database": current_db,
                "test_query": test_value == 1,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"MySQL connection test failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def get_database_info(self) -> Dict[str, Any]:
        """Get comprehensive database information."""
        try:
            engine = create_engine(self.database_url)
            
            with engine.connect() as conn:
                # Database size
                size_query = text("""
                    SELECT 
                        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                    FROM information_schema.tables 
                    WHERE table_schema = :db_name
                """)
                size_result = conn.execute(size_query, {"db_name": self.database_name})
                db_size = size_result.fetchone()[0] or 0
                
                # Table count
                table_query = text("""
                    SELECT COUNT(*) as table_count 
                    FROM information_schema.tables 
                    WHERE table_schema = :db_name
                """)
                table_result = conn.execute(table_query, {"db_name": self.database_name})
                table_count = table_result.fetchone()[0]
                
                # Connection status
                status_query = text("SHOW STATUS LIKE 'Threads_connected'")
                status_result = conn.execute(status_query)
                connections = status_result.fetchone()[1]
                
                # MySQL variables
                charset_query = text("SHOW VARIABLES LIKE 'character_set_database'")
                charset_result = conn.execute(charset_query)
                charset = charset_result.fetchone()[1]
                
            engine.dispose()
            
            return {
                "database_name": self.database_name,
                "size_mb": float(db_size),
                "table_count": int(table_count),
                "active_connections": int(connections),
                "charset": charset,
                "host": self.host,
                "port": self.port,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting database info: {e}")
            return {"error": str(e)}
    
    def optimize_database(self) -> Dict[str, Any]:
        """Optimize MySQL database performance."""
        try:
            engine = create_engine(self.database_url)
            results = []
            
            with engine.connect() as conn:
                # Get all tables
                tables_query = text("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = :db_name
                """)
                tables_result = conn.execute(tables_query, {"db_name": self.database_name})
                tables = [row[0] for row in tables_result]
                
                # Optimize each table
                for table in tables:
                    try:
                        optimize_query = text(f"OPTIMIZE TABLE {table}")
                        result = conn.execute(optimize_query)
                        results.append({
                            "table": table,
                            "status": "optimized"
                        })
                    except Exception as e:
                        results.append({
                            "table": table,
                            "status": "error",
                            "error": str(e)
                        })
                
                conn.commit()
            
            engine.dispose()
            
            return {
                "status": "success",
                "optimized_tables": len([r for r in results if r["status"] == "optimized"]),
                "failed_tables": len([r for r in results if r["status"] == "error"]),
                "details": results,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Database optimization failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def create_backup(self, backup_path: Optional[str] = None) -> Dict[str, Any]:
        """Create MySQL database backup using mysqldump."""
        try:
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"backup_{self.database_name}_{timestamp}.sql"
            
            # Build mysqldump command
            cmd = [
                "mysqldump",
                f"--host={self.host}",
                f"--port={self.port}",
                f"--user={self.username}",
                f"--password={self.password}",
                "--single-transaction",
                "--routines",
                "--triggers",
                self.database_name
            ]
            
            # Execute backup
            with open(backup_path, 'w') as backup_file:
                result = subprocess.run(
                    cmd,
                    stdout=backup_file,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=3600  # 1 hour timeout
                )
            
            if result.returncode == 0:
                # Get backup file size
                backup_size = os.path.getsize(backup_path)
                
                return {
                    "status": "success",
                    "backup_path": backup_path,
                    "backup_size_mb": round(backup_size / 1024 / 1024, 2),
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "status": "error",
                    "error": result.stderr,
                    "timestamp": datetime.now().isoformat()
                }
                
        except subprocess.TimeoutExpired:
            return {
                "status": "error",
                "error": "Backup timeout (1 hour limit exceeded)",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Backup creation failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

# Global MySQL manager instance
mysql_manager = None

def get_mysql_manager(database_url: str = None) -> MySQLManager:
    """Get or create MySQL manager instance."""
    global mysql_manager

    if mysql_manager is None or database_url:
        if not database_url:
            # Determine database URL based on environment
            env = os.getenv("APP_ENV", "development").lower()
            if env == "production":
                database_url = os.getenv("PROD_DATABASE_URL", "mysql+pymysql://saas_user:password@localhost:3306/saas_prod_db")
            elif env == "test":
                database_url = os.getenv("TEST_DATABASE_URL", "mysql+pymysql://root:@localhost:3306/saas_test_db")
            else:  # development
                database_url = os.getenv("DEV_DATABASE_URL", "mysql+pymysql://root:@localhost:3306/saas_dev_db")

        mysql_manager = MySQLManager(database_url)

    return mysql_manager

def setup_mysql_database() -> bool:
    """Set up MySQL database for production."""
    try:
        manager = get_mysql_manager()
        
        # Create database if not exists
        if not manager.create_database_if_not_exists():
            return False
        
        # Test connection
        connection_test = manager.test_connection()
        if connection_test["status"] != "success":
            logger.error(f"MySQL connection test failed: {connection_test}")
            return False
        
        logger.info("MySQL database setup completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"MySQL setup failed: {e}")
        return False
