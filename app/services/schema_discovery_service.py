"""
Enhanced Schema Discovery Service for AI Training

This service provides comprehensive database schema discovery and management
for training AI assistants with tenant-specific database context.
"""

import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import inspect, text, MetaData, Table
from sqlalchemy.exc import SQLAlchemyError

from database import get_db_session
from models import (
    DatabaseConnection, DatabaseTable, DatabaseColumn, 
    TableRelationship, TenantAIProfile
)
from query_agent import DatabaseConnectionManager
from secrets_manager import SecretsManager

logger = logging.getLogger(__name__)


class SchemaDiscoveryService:
    """Service for discovering and managing database schemas for AI training."""
    
    def __init__(self):
        self.secrets_manager = SecretsManager()
        
    def discover_and_store_schema(self, database_connection_id: str, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Discover database schema and store detailed information for AI training.
        
        Args:
            database_connection_id: ID of the database connection
            force_refresh: Whether to force a complete refresh of schema data
            
        Returns:
            Dict containing discovery results and statistics
        """
        db = get_db_session()
        try:
            # Get database connection
            db_conn = db.query(DatabaseConnection).filter(
                DatabaseConnection.id == database_connection_id
            ).first()
            
            if not db_conn:
                raise ValueError(f"Database connection {database_connection_id} not found")
            
            # Check if we need to refresh schema
            if not force_refresh and self._is_schema_fresh(db_conn):
                logger.info(f"Schema for {database_connection_id} is fresh, skipping discovery")
                return {"status": "skipped", "reason": "schema_fresh"}
            
            # Build connection string
            connection_string = self._build_connection_string(db_conn)
            
            # Discover schema
            schema_data = self._discover_database_schema(connection_string)
            
            # Store schema information
            stored_tables = self._store_schema_data(db, db_conn, schema_data)
            
            # Update connection metadata
            db_conn.schema_last_updated = datetime.utcnow()
            db_conn.metadata_info = {
                "last_discovery": datetime.utcnow().isoformat(),
                "tables_count": len(stored_tables),
                "discovery_version": "2.0"
            }
            
            db.commit()
            
            return {
                "status": "success",
                "tables_discovered": len(stored_tables),
                "tables_stored": len(stored_tables),
                "discovery_time": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"Schema discovery failed for {database_connection_id}: {e}")
            raise
        finally:
            db.close()
    
    def _is_schema_fresh(self, db_conn: DatabaseConnection, max_age_hours: int = 24) -> bool:
        """Check if schema data is fresh enough to skip discovery."""
        if not db_conn.schema_last_updated:
            return False
        
        age = datetime.utcnow() - db_conn.schema_last_updated
        return age < timedelta(hours=max_age_hours)
    
    def _build_connection_string(self, db_conn: DatabaseConnection) -> str:
        """Build connection string from database connection object."""
        if db_conn.connection_string:
            # Decrypt stored connection string
            return self.secrets_manager.decrypt_secret(db_conn.connection_string)
        
        # Build connection string from components
        if db_conn.db_type.lower() == 'mysql':
            password = self.secrets_manager.decrypt_secret(db_conn.password_encrypted) if db_conn.password_encrypted else ""
            return f"mysql+pymysql://{db_conn.username}:{password}@{db_conn.host}:{db_conn.port}/{db_conn.database_name}"
        elif db_conn.db_type.lower() == 'postgresql':
            password = self.secrets_manager.decrypt_secret(db_conn.password_encrypted) if db_conn.password_encrypted else ""
            return f"postgresql://{db_conn.username}:{password}@{db_conn.host}:{db_conn.port}/{db_conn.database_name}"
        elif db_conn.db_type.lower() == 'sqlite':
            return f"sqlite:///{db_conn.database_name}"
        else:
            raise ValueError(f"Unsupported database type: {db_conn.db_type}")
    
    def _discover_database_schema(self, connection_string: str) -> Dict[str, Any]:
        """Discover comprehensive database schema information."""
        def discover_operation(db):
            inspector = inspect(db._engine)
            schema_data = {
                "tables": {},
                "relationships": [],
                "metadata": {
                    "database_type": db.dialect,
                    "discovery_time": datetime.utcnow().isoformat()
                }
            }
            
            # Get all table names
            table_names = inspector.get_table_names()
            
            for table_name in table_names:
                # Skip system tables
                if table_name.startswith(('alembic_', 'information_schema', 'pg_', 'mysql')):
                    continue
                
                table_info = self._analyze_table(inspector, table_name, db)
                schema_data["tables"][table_name] = table_info
                
                # Discover relationships
                relationships = self._discover_table_relationships(inspector, table_name)
                schema_data["relationships"].extend(relationships)
            
            return schema_data
        
        return DatabaseConnectionManager.with_database_connection(
            client_db_uri=connection_string,
            operation=discover_operation,
            fallback_data={"tables": {}, "relationships": [], "metadata": {}}
        )
    
    def _analyze_table(self, inspector, table_name: str, db) -> Dict[str, Any]:
        """Analyze a single table and extract comprehensive information."""
        table_info = {
            "name": table_name,
            "columns": [],
            "indexes": [],
            "primary_keys": [],
            "foreign_keys": [],
            "row_count": 0,
            "estimated_size": 0,
            "business_context": self._infer_business_context(table_name)
        }
        
        try:
            # Get columns
            columns = inspector.get_columns(table_name)
            for col in columns:
                column_info = {
                    "name": col["name"],
                    "type": str(col["type"]),
                    "nullable": col["nullable"],
                    "default": str(col["default"]) if col["default"] else None,
                    "primary_key": col.get("primary_key", False),
                    "autoincrement": col.get("autoincrement", False),
                    "business_context": self._infer_column_context(col["name"], str(col["type"]))
                }
                table_info["columns"].append(column_info)
            
            # Get indexes
            indexes = inspector.get_indexes(table_name)
            table_info["indexes"] = [
                {
                    "name": idx["name"],
                    "columns": idx["column_names"],
                    "unique": idx["unique"]
                }
                for idx in indexes
            ]
            
            # Get primary keys
            pk_constraint = inspector.get_pk_constraint(table_name)
            table_info["primary_keys"] = pk_constraint["constrained_columns"]
            
            # Get foreign keys
            foreign_keys = inspector.get_foreign_keys(table_name)
            table_info["foreign_keys"] = [
                {
                    "name": fk["name"],
                    "constrained_columns": fk["constrained_columns"],
                    "referred_table": fk["referred_table"],
                    "referred_columns": fk["referred_columns"]
                }
                for fk in foreign_keys
            ]
            
            # Get row count (with error handling)
            try:
                result = db._engine.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                table_info["row_count"] = result.fetchone()[0]
            except Exception as e:
                logger.warning(f"Could not get row count for {table_name}: {e}")
                table_info["row_count"] = 0
            
        except Exception as e:
            logger.error(f"Error analyzing table {table_name}: {e}")
        
        return table_info
    
    def _discover_table_relationships(self, inspector, table_name: str) -> List[Dict[str, Any]]:
        """Discover relationships for a specific table."""
        relationships = []
        
        try:
            foreign_keys = inspector.get_foreign_keys(table_name)
            
            for fk in foreign_keys:
                relationship = {
                    "source_table": table_name,
                    "target_table": fk["referred_table"],
                    "source_columns": fk["constrained_columns"],
                    "target_columns": fk["referred_columns"],
                    "relationship_type": "many_to_one",  # Default, can be refined
                    "business_context": self._infer_relationship_context(
                        table_name, fk["referred_table"], fk["constrained_columns"]
                    )
                }
                relationships.append(relationship)
        
        except Exception as e:
            logger.error(f"Error discovering relationships for {table_name}: {e}")
        
        return relationships
    
    def _infer_business_context(self, table_name: str) -> Dict[str, Any]:
        """Infer business context from table name."""
        name_lower = table_name.lower()
        
        # Common business domains
        if any(word in name_lower for word in ['user', 'customer', 'client', 'account']):
            return {"domain": "user_management", "description": "User or customer related data"}
        elif any(word in name_lower for word in ['order', 'purchase', 'transaction', 'payment']):
            return {"domain": "sales", "description": "Sales and transaction data"}
        elif any(word in name_lower for word in ['product', 'item', 'inventory', 'stock']):
            return {"domain": "inventory", "description": "Product and inventory data"}
        elif any(word in name_lower for word in ['employee', 'staff', 'hr']):
            return {"domain": "human_resources", "description": "Employee and HR data"}
        elif any(word in name_lower for word in ['log', 'audit', 'history']):
            return {"domain": "audit", "description": "Logging and audit data"}
        else:
            return {"domain": "general", "description": "General business data"}
    
    def _infer_column_context(self, column_name: str, data_type: str) -> Dict[str, Any]:
        """Infer business context from column name and type."""
        name_lower = column_name.lower()
        type_lower = data_type.lower()
        
        context = {"category": "general", "description": "General data column"}
        
        # Identifier patterns
        if name_lower in ['id'] or name_lower.endswith('_id'):
            context = {"category": "identifier", "description": "Unique identifier"}
        # Name patterns
        elif any(word in name_lower for word in ['name', 'title', 'label']):
            context = {"category": "name", "description": "Name or title field"}
        # Date/time patterns
        elif any(word in name_lower for word in ['date', 'time', 'created', 'updated']):
            context = {"category": "timestamp", "description": "Date or time information"}
        # Contact patterns
        elif any(word in name_lower for word in ['email', 'phone', 'address']):
            context = {"category": "contact", "description": "Contact information"}
        # Status patterns
        elif any(word in name_lower for word in ['status', 'state', 'active']):
            context = {"category": "status", "description": "Status or state information"}
        # Measurement patterns
        elif any(word in name_lower for word in ['amount', 'price', 'cost', 'total', 'count']):
            context = {"category": "measurement", "description": "Numerical measurement or amount"}
        
        return context
    
    def _infer_relationship_context(self, source_table: str, target_table: str, columns: List[str]) -> Dict[str, Any]:
        """Infer business context from relationship information."""
        return {
            "description": f"{source_table} references {target_table}",
            "business_meaning": f"Each {source_table} is associated with a {target_table}"
        }

    def _store_schema_data(self, db: Session, db_conn: DatabaseConnection, schema_data: Dict[str, Any]) -> List[str]:
        """Store discovered schema data in the database."""
        stored_tables = []

        try:
            # Clear existing schema data for this connection
            db.query(DatabaseTable).filter(
                DatabaseTable.database_connection_id == db_conn.id
            ).delete()

            # Store tables and columns
            for table_name, table_info in schema_data["tables"].items():
                # Create table record
                db_table = DatabaseTable(
                    database_connection_id=db_conn.id,
                    table_name=table_name,
                    table_type="table",
                    business_name=table_info["business_context"].get("description", table_name),
                    business_description=table_info["business_context"].get("description"),
                    business_domain=table_info["business_context"].get("domain"),
                    row_count=table_info.get("row_count", 0),
                    ai_description=self._generate_ai_description(table_name, table_info),
                    common_queries=self._generate_common_queries(table_name, table_info),
                    query_patterns=self._generate_query_patterns(table_name, table_info)
                )
                db.add(db_table)
                db.flush()  # Get the ID

                # Store columns
                for col_info in table_info["columns"]:
                    db_column = DatabaseColumn(
                        table_id=db_table.id,
                        column_name=col_info["name"],
                        data_type=col_info["type"],
                        is_nullable=col_info["nullable"],
                        is_primary_key=col_info["primary_key"],
                        is_foreign_key=col_info["name"] in [fk["constrained_columns"][0] for fk in table_info["foreign_keys"] if fk["constrained_columns"]],
                        default_value=col_info["default"],
                        business_name=col_info["business_context"].get("description", col_info["name"]),
                        business_description=col_info["business_context"].get("description"),
                        data_category=col_info["business_context"].get("category"),
                        ai_description=self._generate_column_ai_description(col_info),
                        sample_values=self._get_sample_values(db_conn, table_name, col_info["name"])
                    )
                    db.add(db_column)

                stored_tables.append(table_name)

            # Store relationships
            self._store_relationships(db, db_conn, schema_data["relationships"])

            return stored_tables

        except Exception as e:
            logger.error(f"Error storing schema data: {e}")
            raise

    def _generate_ai_description(self, table_name: str, table_info: Dict[str, Any]) -> str:
        """Generate AI-optimized description for a table."""
        domain = table_info["business_context"].get("domain", "general")
        row_count = table_info.get("row_count", 0)
        column_count = len(table_info.get("columns", []))

        description = f"Table '{table_name}' in {domain} domain with {column_count} columns"
        if row_count > 0:
            description += f" containing {row_count} records"

        # Add key column information
        key_columns = [col["name"] for col in table_info.get("columns", []) if col.get("primary_key")]
        if key_columns:
            description += f". Primary key: {', '.join(key_columns)}"

        return description

    def _generate_common_queries(self, table_name: str, table_info: Dict[str, Any]) -> List[str]:
        """Generate common query patterns for a table."""
        queries = [
            f"Show all records from {table_name}",
            f"Count records in {table_name}",
            f"Get recent records from {table_name}"
        ]

        # Add domain-specific queries
        domain = table_info["business_context"].get("domain")
        if domain == "user_management":
            queries.extend([
                f"Find active users in {table_name}",
                f"Search users by name in {table_name}"
            ])
        elif domain == "sales":
            queries.extend([
                f"Calculate total sales from {table_name}",
                f"Find recent orders in {table_name}"
            ])
        elif domain == "inventory":
            queries.extend([
                f"Check stock levels in {table_name}",
                f"Find products by category in {table_name}"
            ])

        return queries

    def _generate_query_patterns(self, table_name: str, table_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate SQL query patterns for a table."""
        patterns = {
            "select_all": f"SELECT * FROM {table_name}",
            "count": f"SELECT COUNT(*) FROM {table_name}",
            "recent": f"SELECT * FROM {table_name} ORDER BY created_at DESC LIMIT 10"
        }

        # Add patterns based on available columns
        columns = [col["name"] for col in table_info.get("columns", [])]

        if "status" in columns:
            patterns["active"] = f"SELECT * FROM {table_name} WHERE status = 'active'"

        if "created_at" in columns:
            patterns["today"] = f"SELECT * FROM {table_name} WHERE DATE(created_at) = CURDATE()"

        if any("name" in col for col in columns):
            name_col = next(col for col in columns if "name" in col)
            patterns["search_by_name"] = f"SELECT * FROM {table_name} WHERE {name_col} LIKE '%{{search_term}}%'"

        return patterns

    def _generate_column_ai_description(self, col_info: Dict[str, Any]) -> str:
        """Generate AI-optimized description for a column."""
        name = col_info["name"]
        data_type = col_info["type"]
        category = col_info["business_context"].get("category", "general")

        description = f"Column '{name}' of type {data_type}"

        if col_info["primary_key"]:
            description += " (Primary Key)"
        elif col_info["business_context"].get("category") == "identifier":
            description += " (Identifier)"

        if not col_info["nullable"]:
            description += " - Required field"

        return description

    def _get_sample_values(self, db_conn: DatabaseConnection, table_name: str, column_name: str) -> List[str]:
        """Get sample values for a column (for AI context)."""
        try:
            connection_string = self._build_connection_string(db_conn)

            def get_samples(db):
                query = f"SELECT DISTINCT {column_name} FROM {table_name} WHERE {column_name} IS NOT NULL LIMIT 5"
                result = db._engine.execute(text(query))
                return [str(row[0]) for row in result.fetchall()]

            return DatabaseConnectionManager.with_database_connection(
                client_db_uri=connection_string,
                operation=get_samples,
                fallback_data=[]
            )
        except Exception as e:
            logger.warning(f"Could not get sample values for {table_name}.{column_name}: {e}")
            return []

    def _store_relationships(self, db: Session, db_conn: DatabaseConnection, relationships: List[Dict[str, Any]]):
        """Store table relationships."""
        for rel in relationships:
            try:
                # Find source and target table IDs
                source_table = db.query(DatabaseTable).filter(
                    DatabaseTable.database_connection_id == db_conn.id,
                    DatabaseTable.table_name == rel["source_table"]
                ).first()

                target_table = db.query(DatabaseTable).filter(
                    DatabaseTable.database_connection_id == db_conn.id,
                    DatabaseTable.table_name == rel["target_table"]
                ).first()

                if source_table and target_table:
                    db_relationship = TableRelationship(
                        source_table_id=source_table.id,
                        target_table_id=target_table.id,
                        relationship_type=rel["relationship_type"],
                        source_columns=rel["source_columns"],
                        target_columns=rel["target_columns"],
                        business_description=rel["business_context"]["description"],
                        ai_description=f"Join {rel['source_table']} with {rel['target_table']} on {', '.join(rel['source_columns'])}"
                    )
                    db.add(db_relationship)

            except Exception as e:
                logger.error(f"Error storing relationship: {e}")
                continue
