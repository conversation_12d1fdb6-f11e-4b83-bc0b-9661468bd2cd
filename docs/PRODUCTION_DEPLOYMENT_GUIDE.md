# Production Deployment Guide

This guide provides step-by-step instructions for deploying the SaaS Backend to production.

## 🚀 **Quick Production Deployment**

### Prerequisites
- Docker and Docker Compose installed
- MySQL server (local or cloud)
- Domain name configured (optional)
- SSL certificates (for HTTPS)

### 1. Environment Setup
```bash
# Clone and navigate to project
cd /path/to/My_SaaS_Backend

# Copy production environment template
cp .env.production .env

# Generate secure JWT secret
python -c "import secrets; print('JWT_SECRET_KEY=' + secrets.token_urlsafe(32))" >> .env

# Edit .env with your configuration
nano .env
```

### 2. Required Environment Variables
```bash
# Database (REQUIRED)
PROD_DATABASE_URL=mysql+pymysql://saas_user:SECURE_PASSWORD@localhost:3306/saas_prod_db

# Authentication (REQUIRED)
JWT_SECRET_KEY=your-generated-secure-jwt-secret-key

# OpenAI (REQUIRED)
OPENAI_API_KEY=your-openai-api-key

# Docker MySQL (REQUIRED for Docker deployment)
MYSQL_ROOT_PASSWORD=secure-root-password
MYSQL_PASSWORD=secure-user-password

# Security (RECOMMENDED)
TRUSTED_HOSTS=yourdomain.com,www.yourdomain.com
ADMIN_PASSWORD=secure-admin-password
```

### 3. Database Setup
```bash
# Option A: Use existing MySQL server
python setup_mysql_all_envs.py --env prod

# Option B: Use Docker MySQL (recommended for quick start)
# Database will be created automatically with Docker Compose
```

### 4. Deploy with Docker
```bash
# Build and start all services
docker-compose -f docker-compose.prod.yml up -d

# Check service status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f
```

### 5. Verify Deployment
```bash
# Health check
curl http://localhost/health

# Test API
curl http://localhost/

# Check database
curl http://localhost/database/mysql/health
```

## 🔧 **Manual Production Setup**

### 1. Install Dependencies
```bash
# Install Python dependencies
pip install -r requirements.txt

# Install additional production dependencies
pip install gunicorn gevent psutil prometheus-client
```

### 2. Database Setup
```bash
# Set up production database
python setup_mysql_all_envs.py --env prod

# Test database connection
python test_mysql.py
```

### 3. Start Production Server
```bash
# Option A: Use startup script (recommended)
python start_production.py

# Option B: Direct Gunicorn
gunicorn main:app \
  --bind 0.0.0.0:8000 \
  --workers 4 \
  --worker-class uvicorn.workers.UvicornWorker \
  --max-requests 1000 \
  --timeout 30 \
  --access-logfile - \
  --error-logfile -

# Option C: Direct Uvicorn (development-like)
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

## 🌐 **Nginx Configuration**

### 1. Install Nginx
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

### 2. Configure Nginx
```bash
# Copy configuration
sudo cp nginx/nginx.conf /etc/nginx/sites-available/saas-backend
sudo ln -s /etc/nginx/sites-available/saas-backend /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 3. SSL/HTTPS Setup (Optional)
```bash
# Install Certbot for Let's Encrypt
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 **Monitoring and Health Checks**

### Health Check Endpoints
- `GET /` - Basic health check
- `GET /health` - Detailed health status
- `GET /health/ready` - Kubernetes readiness probe
- `GET /health/live` - Kubernetes liveness probe
- `GET /database/mysql/health` - Database health

### Monitoring Setup
```bash
# Check application health
curl http://localhost/health

# Monitor logs
tail -f logs/app.log

# Database monitoring
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost/database/mysql/status
```

## 🔒 **Security Checklist**

### Before Production
- [ ] Change all default passwords
- [ ] Generate secure JWT secret key
- [ ] Configure HTTPS/SSL
- [ ] Set up firewall rules
- [ ] Configure rate limiting
- [ ] Review CORS settings
- [ ] Set up monitoring and alerting
- [ ] Configure backup strategy
- [ ] Test disaster recovery

### Security Headers (Nginx handles these)
- [ ] X-Frame-Options
- [ ] X-Content-Type-Options
- [ ] X-XSS-Protection
- [ ] Strict-Transport-Security
- [ ] Content-Security-Policy

## 🔄 **Backup and Recovery**

### Database Backup
```bash
# Manual backup
curl -X POST -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost/database/mysql/backup

# Automated backup (cron job)
0 2 * * * /usr/bin/python3 /path/to/backup_script.py
```

### Application Backup
```bash
# Backup application files
tar -czf saas-backend-$(date +%Y%m%d).tar.gz \
  --exclude=node_modules \
  --exclude=venv \
  --exclude=__pycache__ \
  .
```

## 🚨 **Troubleshooting**

### Common Issues

#### 1. Database Connection Failed
```bash
# Check MySQL service
sudo systemctl status mysql

# Test connection manually
mysql -u saas_user -p -h localhost saas_prod_db

# Check environment variables
echo $PROD_DATABASE_URL
```

#### 2. Application Won't Start
```bash
# Check logs
docker-compose -f docker-compose.prod.yml logs app

# Check environment
python start_production.py --skip-db-check --skip-init

# Validate configuration
python -c "from production_config import ProductionConfig; print(ProductionConfig().__dict__)"
```

#### 3. High Memory Usage
```bash
# Reduce workers
export WORKERS=2

# Check memory usage
docker stats

# Optimize database pool
export DB_POOL_SIZE=5
export DB_MAX_OVERFLOW=10
```

#### 4. SSL Certificate Issues
```bash
# Check certificate
sudo certbot certificates

# Renew certificate
sudo certbot renew

# Test SSL
curl -I https://yourdomain.com
```

## 📈 **Performance Optimization**

### Application Tuning
```bash
# Optimize workers based on CPU
export WORKERS=$(($(nproc) * 2 + 1))

# Tune database connections
export DB_POOL_SIZE=15
export DB_MAX_OVERFLOW=25

# Enable connection pooling
export DB_POOL_RECYCLE=3600
```

### Database Optimization
```bash
# Optimize database
curl -X POST -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost/database/mysql/optimize

# Monitor performance
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost/database/mysql/status
```

## 🔄 **Updates and Maintenance**

### Application Updates
```bash
# Pull latest code
git pull origin main

# Update dependencies
pip install -r requirements.txt

# Restart services
docker-compose -f docker-compose.prod.yml restart app
```

### Database Maintenance
```bash
# Create backup before updates
curl -X POST -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost/database/mysql/backup

# Run database migrations (if any)
# python migrate.py

# Optimize database
curl -X POST -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost/database/mysql/optimize
```

## 📞 **Support and Monitoring**

### Log Locations
- Application logs: `/app/logs/` (in container)
- Nginx logs: `/var/log/nginx/`
- MySQL logs: `/var/log/mysql/`

### Monitoring Commands
```bash
# Service status
docker-compose -f docker-compose.prod.yml ps

# Resource usage
docker stats

# Application health
curl http://localhost/health

# Database status
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost/database/mysql/status
```

This production setup provides a robust, scalable, and secure deployment for your SaaS backend!
