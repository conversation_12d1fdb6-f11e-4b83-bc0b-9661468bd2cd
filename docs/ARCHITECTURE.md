# My SaaS Backend - Architecture Documentation

## Repository Structure

The repository has been restructured to follow Python best practices and improve maintainability. Here's the new organization:

```
My_SaaS_Backend/
├── app/                          # Main application package
│   ├── __init__.py
│   ├── main.py                   # FastAPI application entry point
│   ├── templates/                # Jinja2 HTML templates
│   ├── core/                     # Core application modules
│   │   ├── __init__.py
│   │   ├── auth.py              # Authentication & JWT management
│   │   ├── crud.py              # Database CRUD operations
│   │   ├── database.py          # Database configuration & sessions
│   │   ├── enhanced_security.py # Enhanced security middleware
│   │   ├── input_validation.py  # Input validation & sanitization
│   │   ├── secrets_manager.py   # Secrets management
│   │   └── security_middleware.py # Security middleware
│   ├── models/                   # Database models & schemas
│   │   ├── __init__.py
│   │   └── models.py            # SQLAlchemy models
│   ├── services/                 # Business logic & external services
│   │   ├── __init__.py
│   │   ├── ai_context_service.py
│   │   ├── ai_enhanced_methods.py
│   │   ├── ai_message_processor.py
│   │   ├── mysql_manager.py     # MySQL management utilities
│   │   ├── query_agent.py       # Natural language to SQL
│   │   └── schema_discovery_service.py
│   ├── config/                   # Configuration modules
│   │   ├── __init__.py
│   │   ├── api_versioning.py    # API versioning configuration
│   │   └── production_config.py # Production settings
│   └── utils/                    # Utility functions
│       └── __init__.py
├── tests/                        # Test suite
│   ├── __init__.py
│   ├── conftest.py              # Pytest configuration
│   ├── api/                     # API endpoint tests
│   ├── integration/             # Integration tests
│   ├── unit/                    # Unit tests
│   └── test_*.py               # Individual test files
├── deployment/                   # Deployment configurations
│   ├── docker/                  # Docker configurations
│   │   ├── Dockerfile
│   │   ├── Dockerfile.production
│   │   ├── Dockerfile.simple
│   │   ├── docker-compose.dev.yml
│   │   ├── docker-compose.prod.yml
│   │   └── docker-compose.simple.yml
│   ├── nginx/                   # Nginx configuration
│   │   └── nginx.conf
│   └── mysql-init/              # MySQL initialization scripts
│       └── 01-create-databases.sql
├── scripts/                      # Utility scripts
│   ├── setup_mysql.py
│   ├── setup_mysql_all_envs.py
│   ├── quick_start.py
│   ├── start_production.py
│   ├── run_tests.py
│   └── *.sh                     # Shell scripts
├── docs/                         # Documentation
│   ├── ARCHITECTURE.md          # This file
│   ├── MYSQL_SETUP_GUIDE.md
│   ├── PRODUCTION_DEPLOYMENT_GUIDE.md
│   ├── SECURITY_GUIDE.md
│   ├── TESTING_GUIDE.md
│   └── *_SUMMARY.md
├── config/                       # Configuration files
├── db/                          # Database related files
│   ├── migrations/
│   └── schema/
├── alembic/                     # Database migrations
├── main.py                      # Application entry point
├── pyproject.toml              # Modern Python project configuration
├── requirements.txt            # Python dependencies
├── alembic.ini                 # Alembic configuration
├── pytest.ini                 # Pytest configuration
└── README.md                   # Project documentation
```

## Architecture Overview

### Core Components

1. **Application Layer (`app/`)**
   - Contains the main FastAPI application and all business logic
   - Organized into logical modules for better maintainability

2. **Core Module (`app/core/`)**
   - Authentication and authorization
   - Database operations and session management
   - Security middleware and input validation
   - Secrets management

3. **Models (`app/models/`)**
   - SQLAlchemy database models
   - Pydantic schemas for API validation

4. **Services (`app/services/`)**
   - AI-powered features and integrations
   - External service integrations
   - Business logic that doesn't fit in core

5. **Configuration (`app/config/`)**
   - Environment-specific configurations
   - API versioning setup
   - Production settings

### Key Features

- **Multi-tenant Architecture**: Support for multiple tenants with isolated data
- **AI Integration**: Natural language to SQL conversion using OpenAI
- **Comprehensive Security**: JWT authentication, input validation, rate limiting
- **Database Management**: MySQL with connection pooling and migrations
- **Admin Interface**: Web-based admin panel for user and tenant management
- **API Versioning**: Support for multiple API versions
- **Production Ready**: Docker containerization with nginx reverse proxy

### Development Workflow

1. **Local Development**: Use `docker-compose.dev.yml` for development environment
2. **Testing**: Run tests with `python scripts/run_tests.py`
3. **Production**: Deploy using `docker-compose.prod.yml`

### Security Features

- JWT-based authentication with refresh tokens
- Password hashing using bcrypt
- Input validation and sanitization
- Rate limiting for API endpoints
- SQL injection prevention
- CORS configuration
- Environment-based secrets management

### Database Design

- Multi-tenant data isolation
- User management with role-based access
- Chat and message history tracking
- Query history and analytics
- API key management
- Admin user system

This architecture provides a solid foundation for a scalable SaaS application with proper separation of concerns and modern Python development practices.
