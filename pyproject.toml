[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "my-saas-backend"
version = "1.0.0"
description = "A comprehensive SaaS backend API built with FastAPI"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "SaaS Backend Team", email = "<EMAIL>"}
]
keywords = ["fastapi", "saas", "backend", "api", "mysql", "ai"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Framework :: FastAPI",
]

dependencies = [
    # FastAPI and Core Dependencies
    "fastapi",
    "starlette",
    "pydantic[email]",
    "uvicorn",
    "python-dotenv",
    "jinja2",
    
    # Database Dependencies
    "sqlalchemy>=2.0.0",
    "alembic",
    "psycopg2-binary",
    "pymysql",
    "cryptography",
    
    # AI and LangChain Dependencies
    "openai>=1.0.0",
    "langchain-community",
    "langchain",
    "langchain-openai",
    
    # Authentication & Security
    "passlib[bcrypt]>=1.7.4",
    "python-jose[cryptography]",
    "python-multipart",
    "PyJWT>=2.8.0",
    "bcrypt>=4.0.0,<5.0.0",
    
    # Additional Utilities
    "email-validator",
    "python-dateutil",
    "redis>=4.5.0",
    
    # Production Server Dependencies
    "gunicorn>=21.2.0",
    "gevent>=23.0.0",
    "psutil>=5.9.0",
    
    # Monitoring and Health Checks
    "prometheus-client>=0.17.0",
    
    # Security Dependencies
    "cryptography>=41.0.0",
    "bleach>=6.0.0",
    "user-agents>=2.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "httpx>=0.24.0",
    "factory-boy>=3.3.0",
    "faker>=19.0.0",
    "black",
    "isort",
    "flake8",
    "mypy",
]

[project.urls]
Homepage = "https://github.com/yourusername/my-saas-backend"
Documentation = "https://github.com/yourusername/my-saas-backend/docs"
Repository = "https://github.com/yourusername/my-saas-backend.git"
"Bug Tracker" = "https://github.com/yourusername/my-saas-backend/issues"

[project.scripts]
my-saas-backend = "app.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["app*"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short --strict-markers"
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "api: API tests",
    "slow: Slow running tests",
]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "langchain.*",
    "openai.*",
    "pymysql.*",
    "redis.*",
]
ignore_missing_imports = true
