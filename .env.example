# Backend API Configuration
# API Configuration
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=10000

# Environment
VITE_ENV=development

# Application
VITE_APP_NAME=My SaaS Frontend
VITE_APP_VERSION=1.0.0

# Features
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_ANALYTICS=false

# Error Tracking (Sentry)
VITE_SENTRY_DSN=
VITE_SENTRY_ENVIRONMENT=development

# Performance Monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=false

# Security
VITE_ENABLE_CSP=true
VITE_CSRF_TOKEN_HEADER=X-CSRF-Token
VITE_API_TIMEOUT=10000

# Development Settings
VITE_ENV=development
