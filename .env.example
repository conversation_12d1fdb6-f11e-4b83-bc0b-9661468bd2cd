# Environment Configuration Example
# Copy this file to .env and update with your actual values

# Application Environment
APP_ENV=development

# Required: OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Database Configuration - MySQL for ALL environments
# Development Database (MySQL)
DEV_DATABASE_URL=mysql+pymysql://root:@localhost:3306/saas_dev_db

# Production Database (MySQL)
PROD_DATABASE_URL=mysql+pymysql://saas_user:password@localhost:3306/saas_prod_db

# Test Database (MySQL)
TEST_DATABASE_URL=mysql+pymysql://root:@localhost:3306/saas_test_db

# Legacy database URI (for backward compatibility)
DB_URI=mysql+pymysql://user:password@localhost:3306/dev_db

# MySQL Connection Pool Settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_RECYCLE=3600
DB_CONNECT_TIMEOUT=10
DB_READ_TIMEOUT=30
DB_WRITE_TIMEOUT=30

# MySQL SSL Configuration (for production)
DB_SSL_CA=
DB_SSL_CERT=
DB_SSL_KEY=
DB_SSL_VERIFY_CERT=true

# JWT Authentication & Security
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Security Settings
ENABLE_CSP=true
ADMIN_IP_WHITELIST=127.0.0.1,::1
ENABLE_RATE_LIMITING=true
DEFAULT_RATE_LIMIT=1000
AUTH_RATE_LIMIT=5

# Admin Dashboard Credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=My SaaS Backend
API_TIMEOUT=30
MAX_QUERY_LENGTH=1000

# CORS Settings
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173", "http://localhost:8080"]

# Logging
LOG_LEVEL=INFO
SQL_ECHO=false
DEBUG=True

# Email Configuration (optional)
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Redis Configuration (optional, for caching)
REDIS_URL=redis://localhost:6379

# File Upload Settings
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes
UPLOAD_FOLDER=./uploads
