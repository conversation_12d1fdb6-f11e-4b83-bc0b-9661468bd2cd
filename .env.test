# Test Environment Configuration
# Used for running automated tests

# Application Environment
APP_ENV=test
DEBUG=false

# MySQL Database Configuration - Test Database
TEST_DATABASE_URL=mysql+pymysql://root:@localhost:3306/saas_test_db

# Use test database for all environments during testing
DEV_DATABASE_URL=mysql+pymysql://root:@localhost:3306/saas_test_db
PROD_DATABASE_URL=mysql+pymysql://root:@localhost:3306/saas_test_db

# MySQL Connection Pool Settings (minimal for testing)
DB_POOL_SIZE=2
DB_MAX_OVERFLOW=5
DB_POOL_RECYCLE=300
DB_CONNECT_TIMEOUT=5
DB_READ_TIMEOUT=10
DB_WRITE_TIMEOUT=10

# JWT Authentication (test keys)
JWT_SECRET_KEY=test-jwt-secret-key-for-testing-only
ACCESS_TOKEN_EXPIRE_MINUTES=5
REFRESH_TOKEN_EXPIRE_DAYS=1

# OpenAI Configuration (use test key or mock)
OPENAI_API_KEY=test-key-or-mock

# Security Settings (disabled for testing)
ENABLE_CSP=false
ADMIN_IP_WHITELIST=127.0.0.1,::1
ENABLE_RATE_LIMITING=false
DEFAULT_RATE_LIMIT=100000
AUTH_RATE_LIMIT=1000

# CORS Settings (permissive for testing)
ALLOWED_ORIGINS=*

# Admin Credentials (test only)
ADMIN_USERNAME=test_admin
ADMIN_PASSWORD=test_password

# Logging (minimal for testing)
LOG_LEVEL=WARNING
LOG_FORMAT=text
SQL_ECHO=false

# Email Configuration (disabled for testing)
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
EMAIL_FROM=test@localhost

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379/1

# Monitoring (disabled for testing)
ENABLE_METRICS=false
SENTRY_DSN=

# File Upload Settings (minimal for testing)
MAX_FILE_SIZE=1048576  # 1MB
ALLOWED_FILE_TYPES=jpg,png,txt

# API Settings
API_V1_PREFIX=/api/v1
API_TITLE=My SaaS Backend (Test)
API_VERSION=1.0.0-test

# Test Server Settings
WORKERS=1
HOST=127.0.0.1
PORT=8001
RELOAD=false

# Backup Settings (disabled for testing)
BACKUP_ENABLED=false
