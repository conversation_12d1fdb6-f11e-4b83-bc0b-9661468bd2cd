# Development Environment Configuration
# Copy this file to .env for development

# Application Environment
APP_ENV=development
DEBUG=true

# MySQL Database Configuration - ALL environments use MySQL
# Development Database (using root for simplicity)
DEV_DATABASE_URL=mysql+pymysql://root:@localhost:3306/saas_dev_db

# Test Database (for running tests)
TEST_DATABASE_URL=mysql+pymysql://root:@localhost:3306/saas_test_db

# Production Database (not used in development)
PROD_DATABASE_URL=mysql+pymysql://saas_user:password@localhost:3306/saas_prod_db

# MySQL Connection Pool Settings (smaller for development)
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_RECYCLE=3600
DB_CONNECT_TIMEOUT=10
DB_READ_TIMEOUT=30
DB_WRITE_TIMEOUT=30

# JWT Authentication (development keys - not secure)
JWT_SECRET_KEY=dev-jwt-secret-key-not-for-production-use-only
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=30

# OpenAI Configuration (REQUIRED)
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Security Settings (relaxed for development)
ENABLE_CSP=false
ADMIN_IP_WHITELIST=127.0.0.1,::1,localhost
ENABLE_RATE_LIMITING=false
DEFAULT_RATE_LIMIT=10000
AUTH_RATE_LIMIT=50

# CORS Settings (permissive for development)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:5174,http://localhost:8080

# Admin Credentials (development only)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# Logging (verbose for development)
LOG_LEVEL=DEBUG
LOG_FORMAT=text
SQL_ECHO=true

# Email Configuration (optional for development)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
EMAIL_FROM=dev@localhost

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379/0

# Monitoring (disabled for development)
ENABLE_METRICS=false
SENTRY_DSN=

# File Upload Settings
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# API Settings
API_V1_PREFIX=/api/v1
API_TITLE=My SaaS Backend (Development)
API_VERSION=1.0.0-dev

# Development Server Settings
WORKERS=1
HOST=0.0.0.0
PORT=8000
RELOAD=true

# Backup Settings (disabled for development)
BACKUP_ENABLED=false
