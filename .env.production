# Production Environment Configuration
# Copy this file to .env for production deployment

# Application Environment
APP_ENV=production
DEBUG=false

# MySQL Database Configuration - ALL environments use MySQL
# Development Database
DEV_DATABASE_URL=mysql+pymysql://root:@localhost:3306/saas_dev_db

# Production Database (REQUIRED)
PROD_DATABASE_URL=mysql+pymysql://saas_user:YOUR_MYSQL_PASSWORD@localhost:3306/saas_prod_db

# Test Database
TEST_DATABASE_URL=mysql+pymysql://root:@localhost:3306/saas_test_db

# MySQL Connection Pool Settings
DB_POOL_SIZE=15
DB_MAX_OVERFLOW=25
DB_POOL_RECYCLE=3600
DB_CONNECT_TIMEOUT=10
DB_READ_TIMEOUT=30
DB_WRITE_TIMEOUT=30

# JWT Authentication (REQUIRED - Generate secure keys)
JWT_SECRET_KEY=GENERATE_SECURE_JWT_SECRET_KEY_HERE
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# OpenAI Configuration (REQUIRED)
OPENAI_API_KEY=your_openai_api_key_here

# Security Settings
ENABLE_CSP=true
ADMIN_IP_WHITELIST=127.0.0.1,::1
ENABLE_RATE_LIMITING=true
DEFAULT_RATE_LIMIT=1000
AUTH_RATE_LIMIT=5

# CORS Settings (Update with your domain)
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Admin Credentials (CHANGE THESE!)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=CHANGE_THIS_SECURE_PASSWORD

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
SQL_ECHO=false

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Redis Configuration (Optional - for caching)
REDIS_URL=redis://localhost:6379/0

# Monitoring (Optional)
ENABLE_METRICS=true
SENTRY_DSN=your_sentry_dsn_here

# File Upload Settings
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# API Settings
API_V1_PREFIX=/api/v1
API_TITLE=My SaaS Backend
API_VERSION=1.0.0

# Production Server Settings
WORKERS=4
HOST=0.0.0.0
PORT=8000
RELOAD=false

# SSL/TLS Settings (for HTTPS)
SSL_KEYFILE=
SSL_CERTFILE=
SSL_CA_CERTS=

# Backup Settings
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Docker MySQL Settings (for docker-compose)
MYSQL_ROOT_PASSWORD=SECURE_ROOT_PASSWORD_HERE
MYSQL_PASSWORD=SECURE_USER_PASSWORD_HERE
