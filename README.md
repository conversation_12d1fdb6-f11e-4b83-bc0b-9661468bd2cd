# My SaaS Frontend

This is the main frontend repository for My SaaS application. It is built using Re<PERSON>, Vite, and TanStack Query. This application is fully dockerized and uses docker-compose to manage the containers.

## Setting up the project

### Step 1
Install your preferred IDE. We recommend using VS Code.

### Step 2
Install Docker Desktop

### Step 3
Create the docker volumes
```bash
docker volume create saas-frontend-node-modules
```

### Step 4
Clone the repository via terminal & cd into the directory
```bash
git clone <your-repo-url>
cd My_SaaS_Frontend
```

### Step 5
Set up environment variables
```bash
cp .env.example .env.docker
```
Edit `.env.docker` file and configure your API endpoints:
```env
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_ENV=development
```

### Step 6
Build & start the containers
```bash
docker compose up --build -d
```

### Step 7
Setup testing
```bash
docker exec -it my_saas_frontend-frontend-1 /bin/bash
> npm test
```

## Useful commands:
- **Building docker containers** - `docker compose up --build -d`
- **View all containers** - `docker ps -a`
- **Stop all containers** - `docker stop $(docker ps -aq)`
- **Prune all stopped containers** - `docker system prune -a`
- **Login to frontend container** - `docker exec -it my_saas_frontend-frontend-1 /bin/bash`
- **Running tests locally** - `npm test`
- **Running specific test** - `npm test -- --run src/components/ComponentName.test.jsx`
- **Build for production** - `npm run build`
- **Preview production build** - `npm run preview`

## Useful docs
- [React Documentation](https://react.dev/)
- [Vite Documentation](https://vitejs.dev/)
- [TanStack Query Documentation](https://tanstack.com/query/latest)

## ⚠️ Important
**For removing a component:**
1. Remove the component's usage from other components, and deploy
2. Remove the component file, and deploy

**For adding a component:**
1. Create the component file, and deploy
2. Add the component's usage to other components, and deploy

**Environment variables:**
- All environment variables must be prefixed with `VITE_`
- Never commit sensitive data to environment files
- Always test environment changes in development before production

## Application Access
Once running, you can access:
- **Frontend Application**: http://localhost:5173
- **Development Tools**: Available in browser dev tools
