# Database Schema Report
Generated: 2025-07-18T08:27:04.490753
Engine: mysql+pymysql://saas_user:***@mysql_dev:3306/saas_dev_db
Dialect: mysql

## Table Summary

| Table | Columns | Rows | Primary Key | Foreign Keys |
|-------|---------|------|-------------|--------------|
| admin_users | 9 | 0 | id | 0 |
| ai_training_feedback | 20 | 0 | id | 3 |
| api_keys | 11 | 0 | id | 1 |
| chats | 8 | 2 | id | 2 |
| database_columns | 22 | 0 | id | 1 |
| database_connections | 18 | 0 | id | 1 |
| database_tables | 15 | 0 | id | 1 |
| messages | 8 | 10 | id | 2 |
| query_history | 17 | 0 | id | 4 |
| schema_type_preferences | 3 | 10 | mysql_type | 0 |
| table_relationships | 12 | 0 | id | 2 |
| tenant_ai_profiles | 20 | 0 | id | 2 |
| tenant_users | 12 | 1 | id | 1 |
| tenants | 8 | 1 | id | 0 |
| users | 10 | 1 | id | 0 |

## Table: admin_users

**Row Count**: 0

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| name | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| phone | INTEGER | Yes |  | No | No |
| role | VARCHAR(50) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| email | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| password | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| id | INTEGER | No |  | Yes | No |
| created_at | DATETIME | Yes | (now()) | No | No |
| updated_at | DATETIME | Yes | (now()) | No | No |
| status | VARCHAR(20) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |

### Indexes

- `ix_admin_users_email` on `email` (UNIQUE)

---

## Table: ai_training_feedback

**Row Count**: 0

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| database_connection_id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| tenant_user_id | INTEGER | No |  | No | No |
| query_history_id | INTEGER | Yes |  | No | No |
| natural_language_query | TEXT COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| generated_sql | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| ai_response | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| feedback_type | VARCHAR(50) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| feedback_score | INTEGER | Yes |  | No | No |
| user_feedback | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| corrected_sql | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| expected_result | JSON | Yes |  | No | No |
| query_intent | VARCHAR(100) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| tables_mentioned | JSON | Yes |  | No | No |
| business_context | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| learning_priority | VARCHAR(20) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| processed_for_training | TINYINT | No |  | No | No |
| training_notes | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| created_at | DATETIME | Yes | (now()) | No | No |
| updated_at | DATETIME | Yes | (now()) | No | No |

### Foreign Keys

- `database_connection_id` → `database_connections.id`
- `tenant_user_id` → `tenant_users.id`
- `query_history_id` → `query_history.id`

### Indexes

- `database_connection_id` on `database_connection_id`
- `query_history_id` on `query_history_id`
- `tenant_user_id` on `tenant_user_id`

---

## Table: api_keys

**Row Count**: 0

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | INTEGER | No |  | Yes | No |
| tenant_id | INTEGER | No |  | No | No |
| service_name | VARCHAR(100) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| key_name | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| encrypted_key | TEXT COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| status | VARCHAR(20) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| created_at | DATETIME | Yes | (now()) | No | No |
| updated_at | DATETIME | Yes | (now()) | No | No |
| last_used | DATETIME | Yes |  | No | No |
| usage_count | INTEGER | Yes |  | No | No |
| monthly_usage | JSON | Yes |  | No | No |

### Foreign Keys

- `tenant_id` → `tenants.id`

### Indexes

- `tenant_id` on `tenant_id`

---

## Table: chats

**Row Count**: 2

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | INTEGER | No |  | Yes | No |
| tenant_user_id | INTEGER | No |  | No | No |
| database_connection_id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| title | VARCHAR(500) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| description | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| created_at | DATETIME | Yes | (now()) | No | No |
| updated_at | DATETIME | Yes | (now()) | No | No |
| settings | JSON | Yes |  | No | No |

### Foreign Keys

- `tenant_user_id` → `tenant_users.id`
- `database_connection_id` → `database_connections.id`

### Indexes

- `database_connection_id` on `database_connection_id`
- `tenant_user_id` on `tenant_user_id`

---

## Table: database_columns

**Row Count**: 0

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| table_id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| column_name | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| data_type | VARCHAR(100) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| is_nullable | TINYINT | No |  | No | No |
| is_primary_key | TINYINT | No |  | No | No |
| is_foreign_key | TINYINT | No |  | No | No |
| is_unique | TINYINT | No |  | No | No |
| is_indexed | TINYINT | No |  | No | No |
| default_value | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| max_length | INTEGER | Yes |  | No | No |
| business_name | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| business_description | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| data_category | VARCHAR(100) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| sample_values | JSON | Yes |  | No | No |
| value_patterns | JSON | Yes |  | No | No |
| data_quality_notes | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| ai_description | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| common_filters | JSON | Yes |  | No | No |
| aggregation_patterns | JSON | Yes |  | No | No |
| created_at | DATETIME | Yes | (now()) | No | No |
| updated_at | DATETIME | Yes | (now()) | No | No |

### Foreign Keys

- `table_id` → `database_tables.id`

### Indexes

- `table_id` on `table_id`

---

## Table: database_connections

**Row Count**: 0

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| tenant_id | INTEGER | No |  | No | No |
| name | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| db_type | VARCHAR(50) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| host | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| port | INTEGER | Yes |  | No | No |
| database_name | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| username | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| password_encrypted | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| connection_string | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| status | VARCHAR(20) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| created_at | DATETIME | Yes | (now()) | No | No |
| updated_at | DATETIME | Yes | (now()) | No | No |
| last_tested | DATETIME | Yes |  | No | No |
| metadata_info | JSON | Yes |  | No | No |
| business_context | JSON | Yes |  | No | No |
| ai_training_data | JSON | Yes |  | No | No |
| schema_last_updated | DATETIME | Yes |  | No | No |

### Foreign Keys

- `tenant_id` → `tenants.id`

### Indexes

- `tenant_id` on `tenant_id`

---

## Table: database_tables

**Row Count**: 0

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| database_connection_id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| table_name | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| table_type | VARCHAR(50) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| business_name | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| business_description | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| business_domain | VARCHAR(100) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| row_count | INTEGER | Yes |  | No | No |
| estimated_size_mb | FLOAT | Yes |  | No | No |
| last_analyzed | DATETIME | Yes |  | No | No |
| common_queries | JSON | Yes |  | No | No |
| query_patterns | JSON | Yes |  | No | No |
| ai_description | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| created_at | DATETIME | Yes | (now()) | No | No |
| updated_at | DATETIME | Yes | (now()) | No | No |

### Foreign Keys

- `database_connection_id` → `database_connections.id`

### Indexes

- `database_connection_id` on `database_connection_id`

---

## Table: messages

**Row Count**: 10

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | INTEGER | No |  | Yes | No |
| chat_id | INTEGER | No |  | No | No |
| tenant_user_id | INTEGER | Yes |  | No | No |
| role | VARCHAR(20) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| content | TEXT COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| created_at | DATETIME | Yes | (now()) | No | No |
| updated_at | DATETIME | Yes | (now()) | No | No |
| metadata_info | JSON | Yes |  | No | No |

### Foreign Keys

- `chat_id` → `chats.id`
- `tenant_user_id` → `tenant_users.id`

### Indexes

- `chat_id` on `chat_id`
- `tenant_user_id` on `tenant_user_id`

---

## Table: query_history

**Row Count**: 0

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | INTEGER | No |  | Yes | No |
| tenant_user_id | INTEGER | No |  | No | No |
| database_connection_id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| message_id | INTEGER | Yes |  | No | No |
| chat_id | INTEGER | Yes |  | No | No |
| natural_language_query | TEXT COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| generated_sql | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| query_result | JSON | Yes |  | No | No |
| execution_status | VARCHAR(20) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| execution_time | INTEGER | Yes |  | No | No |
| rows_affected | INTEGER | Yes |  | No | No |
| error_message | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| query_complexity | VARCHAR(20) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| tables_involved | JSON | Yes |  | No | No |
| query_source | VARCHAR(20) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| created_at | DATETIME | Yes | (now()) | No | No |
| updated_at | DATETIME | Yes | (now()) | No | No |

### Foreign Keys

- `tenant_user_id` → `tenant_users.id`
- `database_connection_id` → `database_connections.id`
- `message_id` → `messages.id`
- `chat_id` → `chats.id`

### Indexes

- `chat_id` on `chat_id`
- `database_connection_id` on `database_connection_id`
- `message_id` on `message_id`
- `tenant_user_id` on `tenant_user_id`

---

## Table: schema_type_preferences

**Row Count**: 10

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| mysql_type | VARCHAR(50) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| preferred_display | VARCHAR(50) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| created_at | TIMESTAMP | Yes | CURRENT_TIMESTAMP | No | No |

---

## Table: table_relationships

**Row Count**: 0

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| source_table_id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| target_table_id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| relationship_type | VARCHAR(50) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| source_columns | JSON | No |  | No | No |
| target_columns | JSON | No |  | No | No |
| relationship_name | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| business_description | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| join_patterns | JSON | Yes |  | No | No |
| ai_description | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| created_at | DATETIME | Yes | (now()) | No | No |
| updated_at | DATETIME | Yes | (now()) | No | No |

### Foreign Keys

- `source_table_id` → `database_tables.id`
- `target_table_id` → `database_tables.id`

### Indexes

- `source_table_id` on `source_table_id`
- `target_table_id` on `target_table_id`

---

## Table: tenant_ai_profiles

**Row Count**: 0

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| tenant_id | INTEGER | No |  | No | No |
| database_connection_id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| business_domain | VARCHAR(100) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| industry_vertical | VARCHAR(100) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| business_description | TEXT COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| preferred_query_style | VARCHAR(50) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| response_tone | VARCHAR(50) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| explanation_level | VARCHAR(50) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| business_glossary | JSON | Yes |  | No | No |
| common_abbreviations | JSON | Yes |  | No | No |
| preferred_terminology | JSON | Yes |  | No | No |
| successful_query_patterns | JSON | Yes |  | No | No |
| problematic_areas | JSON | Yes |  | No | No |
| improvement_priorities | JSON | Yes |  | No | No |
| query_success_rate | FLOAT | Yes |  | No | No |
| user_satisfaction_score | FLOAT | Yes |  | No | No |
| last_performance_review | DATETIME | Yes |  | No | No |
| created_at | DATETIME | Yes | (now()) | No | No |
| updated_at | DATETIME | Yes | (now()) | No | No |

### Foreign Keys

- `tenant_id` → `tenants.id`
- `database_connection_id` → `database_connections.id`

### Indexes

- `database_connection_id` on `database_connection_id`
- `tenant_id` on `tenant_id`

---

## Table: tenant_users

**Row Count**: 1

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| name | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| role | VARCHAR(50) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| phone | INTEGER | Yes |  | No | No |
| tenant_id | INTEGER | No |  | No | No |
| hashed_password | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| is_superuser | TINYINT | Yes |  | No | No |
| last_login | DATETIME | Yes |  | No | No |
| id | INTEGER | No |  | Yes | No |
| created_at | DATETIME | Yes | (now()) | No | No |
| updated_at | DATETIME | Yes | (now()) | No | No |
| status | VARCHAR(20) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| email | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |

### Foreign Keys

- `tenant_id` → `tenants.id`

### Indexes

- `ix_tenant_users_email` on `email`
- `tenant_id` on `tenant_id`

---

## Table: tenants

**Row Count**: 1

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| employer_name | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| size | INTEGER | Yes |  | No | No |
| phone | INTEGER | Yes |  | No | No |
| id | INTEGER | No |  | Yes | No |
| created_at | DATETIME | Yes | (now()) | No | No |
| updated_at | DATETIME | Yes | (now()) | No | No |
| status | VARCHAR(20) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| email | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |

### Indexes

- `ix_tenants_email` on `email`
- `ix_tenants_employer_name` on `employer_name`

---

## Table: users

**Row Count**: 1

### Columns

| Name | Type | Nullable | Default | Auto Inc | Primary Key |
|------|------|----------|---------|----------|-------------|
| id | VARCHAR(36) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| email | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| username | VARCHAR(100) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| full_name | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | Yes |  | No | No |
| hashed_password | VARCHAR(255) COLLATE "utf8mb4_unicode_ci" | No |  | No | No |
| is_active | TINYINT | Yes |  | No | No |
| is_superuser | TINYINT | Yes |  | No | No |
| created_at | DATETIME | Yes | (now()) | No | No |
| updated_at | DATETIME | Yes | (now()) | No | No |
| last_login | DATETIME | Yes |  | No | No |

### Indexes

- `ix_users_email` on `email` (UNIQUE)
- `ix_users_username` on `username` (UNIQUE)

---
