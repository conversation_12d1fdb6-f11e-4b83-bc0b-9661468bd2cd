#!/usr/bin/env python3
"""
Database utilities for schema inspection and management.
"""

import sys
import os
from datetime import datetime

# Add the project root directory to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from database import engine
from sqlalchemy import text, inspect
import logging

logger = logging.getLogger(__name__)


def get_preferred_data_types():
    """Get user's preferred data type mappings from database."""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT mysql_type, preferred_display FROM schema_type_preferences"))
            return {row[0]: row[1] for row in result.fetchall()}
    except Exception as e:
        logger.warning(f"Could not load type preferences: {e}")
        # Return default mappings
        return {
            'VARCHAR': 'string',
            'CHAR': 'string',
            'INTEGER': 'int',
            'INT': 'int',
            'BIGINT': 'int',
            'SMALLINT': 'int',
            'TINYINT': 'int',
            'TEXT': 'text',
            'LONGTEXT': 'text',
            'MEDIUMTEXT': 'text'
        }


def convert_mysql_type_to_preferred(mysql_type_str, type_preferences):
    """Convert MySQL type string to user's preferred format."""
    # Extract base type from full type string (e.g., "VARCHAR(255)" -> "VARCHAR")
    base_type = mysql_type_str.split('(')[0].upper()

    # Check if we have a preference for this type
    if base_type in type_preferences:
        preferred = type_preferences[base_type]

        # For string types, don't include length specification
        if preferred == 'string':
            return 'string'
        # For int types, don't include length specification
        elif preferred == 'int':
            return 'int'
        # For text types, keep as text
        elif preferred == 'text':
            return 'text'

    # If no preference found, return the original type
    return mysql_type_str


def inspect_database_schema():
    """
    Inspect and return complete database schema information.
    """
    inspector = inspect(engine)
    schema_info = {
        'tables': {},
        'metadata': {
            'engine': str(engine.url),
            'dialect': engine.dialect.name,
            'generated_at': datetime.now().isoformat()
        }
    }
    
    # Get all table names
    table_names = inspector.get_table_names()
    
    for table_name in table_names:
        if table_name.startswith('alembic_'):
            continue  # Skip alembic internal tables
            
        table_info = {
            'columns': [],
            'indexes': [],
            'foreign_keys': [],
            'primary_keys': [],
            'row_count': 0
        }
        
        # Get columns
        columns = inspector.get_columns(table_name)
        for col in columns:
            table_info['columns'].append({
                'name': col['name'],
                'type': str(col['type']),
                'nullable': col['nullable'],
                'default': col['default'],
                'autoincrement': col.get('autoincrement', False),
                'primary_key': col.get('primary_key', False)
            })
        
        # Get indexes
        indexes = inspector.get_indexes(table_name)
        for idx in indexes:
            table_info['indexes'].append({
                'name': idx['name'],
                'columns': idx['column_names'],
                'unique': idx['unique']
            })
        
        # Get foreign keys
        foreign_keys = inspector.get_foreign_keys(table_name)
        for fk in foreign_keys:
            table_info['foreign_keys'].append({
                'name': fk['name'],
                'constrained_columns': fk['constrained_columns'],
                'referred_table': fk['referred_table'],
                'referred_columns': fk['referred_columns']
            })
        
        # Get primary keys
        pk = inspector.get_pk_constraint(table_name)
        table_info['primary_keys'] = pk['constrained_columns']
        
        # Get row count
        with engine.connect() as conn:
            result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            table_info['row_count'] = result.fetchone()[0]
        
        schema_info['tables'][table_name] = table_info
    
    return schema_info

def generate_schema_documentation():
    """
    Generate comprehensive schema documentation.
    """
    schema_info = inspect_database_schema()
    
    doc = []
    doc.append("# Database Schema Report")
    doc.append(f"Generated: {schema_info['metadata']['generated_at']}")
    doc.append(f"Engine: {schema_info['metadata']['engine']}")
    doc.append(f"Dialect: {schema_info['metadata']['dialect']}")
    doc.append("")
    
    # Table summary
    doc.append("## Table Summary")
    doc.append("")
    doc.append("| Table | Columns | Rows | Primary Key | Foreign Keys |")
    doc.append("|-------|---------|------|-------------|--------------|")
    
    for table_name, table_info in schema_info['tables'].items():
        col_count = len(table_info['columns'])
        row_count = table_info['row_count']
        pk = ', '.join(table_info['primary_keys'])
        fk_count = len(table_info['foreign_keys'])
        
        doc.append(f"| {table_name} | {col_count} | {row_count} | {pk} | {fk_count} |")
    
    doc.append("")
    
    # Detailed table information
    for table_name, table_info in schema_info['tables'].items():
        doc.append(f"## Table: {table_name}")
        doc.append("")
        doc.append(f"**Row Count**: {table_info['row_count']}")
        doc.append("")
        
        # Columns
        doc.append("### Columns")
        doc.append("")
        doc.append("| Name | Type | Nullable | Default | Auto Inc | Primary Key |")
        doc.append("|------|------|----------|---------|----------|-------------|")
        
        for col in table_info['columns']:
            nullable = "Yes" if col['nullable'] else "No"
            default = str(col['default']) if col['default'] is not None else ""
            auto_inc = "Yes" if col['autoincrement'] else "No"
            pk = "Yes" if col['primary_key'] else "No"
            
            doc.append(f"| {col['name']} | {col['type']} | {nullable} | {default} | {auto_inc} | {pk} |")
        
        doc.append("")
        
        # Foreign Keys
        if table_info['foreign_keys']:
            doc.append("### Foreign Keys")
            doc.append("")
            for fk in table_info['foreign_keys']:
                constrained = ', '.join(fk['constrained_columns'])
                referred = ', '.join(fk['referred_columns'])
                doc.append(f"- `{constrained}` → `{fk['referred_table']}.{referred}`")
            doc.append("")
        
        # Indexes
        if table_info['indexes']:
            doc.append("### Indexes")
            doc.append("")
            for idx in table_info['indexes']:
                columns = ', '.join(idx['columns'])
                unique = " (UNIQUE)" if idx['unique'] else ""
                doc.append(f"- `{idx['name']}` on `{columns}`{unique}")
            doc.append("")
        
        doc.append("---")
        doc.append("")
    
    return '\n'.join(doc)

def export_schema_to_sql():
    """
    Export current schema as SQL DDL statements using preferred data types.
    """
    inspector = inspect(engine)
    tables = []

    # Get user's preferred data type mappings
    type_preferences = get_preferred_data_types()

    # Get all table names (database-agnostic)
    table_names = inspector.get_table_names()

    # Filter out alembic tables and system tables
    table_names = [name for name in table_names if not name.startswith('alembic_') and name != 'schema_type_preferences']
    table_names.sort()

    for table_name in table_names:
        # Generate CREATE TABLE statement from inspection
        try:
            columns = inspector.get_columns(table_name)
            pk_constraint = inspector.get_pk_constraint(table_name)
            foreign_keys = inspector.get_foreign_keys(table_name)
            indexes = inspector.get_indexes(table_name)

            # Build CREATE TABLE statement
            create_parts = [f"CREATE TABLE {table_name} ("]

            # Add columns
            column_defs = []
            for col in columns:
                # Convert MySQL type to user's preferred format
                preferred_type = convert_mysql_type_to_preferred(str(col['type']), type_preferences)
                col_def = f"\t{col['name']} {preferred_type}"
                if not col['nullable']:
                    col_def += " NOT NULL"
                if col['default'] is not None:
                    if isinstance(col['default'], str) and col['default'].startswith('('):
                        col_def += f" DEFAULT {col['default']}"
                    else:
                        col_def += f" DEFAULT '{col['default']}'"
                column_defs.append(col_def)

            # Add primary key
            if pk_constraint['constrained_columns']:
                pk_cols = ', '.join(pk_constraint['constrained_columns'])
                column_defs.append(f"\tPRIMARY KEY ({pk_cols})")

            # Add foreign keys
            for fk in foreign_keys:
                fk_cols = ', '.join(fk['constrained_columns'])
                ref_cols = ', '.join(fk['referred_columns'])
                column_defs.append(f"\tFOREIGN KEY({fk_cols}) REFERENCES {fk['referred_table']} ({ref_cols})")

            # Add unique constraints
            for idx in indexes:
                if idx['unique'] and idx['name'] != 'PRIMARY':
                    idx_cols = ', '.join(idx['column_names'])
                    column_defs.append(f"\tUNIQUE ({idx_cols})")

            create_parts.append(',\n'.join(column_defs))
            create_parts.append(");")

            tables.append('\n'.join(create_parts))
            tables.append("")

            # Add non-unique indexes
            for idx in indexes:
                if not idx['unique'] and idx['name'] != 'PRIMARY':
                    idx_cols = ', '.join(idx['column_names'])
                    tables.append(f"CREATE INDEX {idx['name']} ON {table_name} ({idx_cols});")

            tables.append("")

        except Exception as e:
            print(f"Warning: Could not export table {table_name}: {e}")
            continue

    return '\n'.join(tables)

def main():
    """
    Main function to generate and save schema documentation.
    """
    print("🔍 Generating database schema documentation...")
    
    try:
        # Generate documentation
        doc = generate_schema_documentation()
        
        # Save to file
        doc_path = os.path.join(os.path.dirname(__file__), 'schema_report.md')
        with open(doc_path, 'w') as f:
            f.write(doc)
        
        print(f"✅ Schema documentation saved to: {doc_path}")
        
        # Generate SQL export
        sql_export = export_schema_to_sql()
        sql_path = os.path.join(os.path.dirname(__file__), 'current_schema.sql')
        with open(sql_path, 'w') as f:
            f.write(f"-- Current Database Schema Export\n")
            f.write(f"-- Generated: {datetime.now().isoformat()}\n\n")
            f.write(sql_export)
        
        print(f"✅ SQL schema export saved to: {sql_path}")
        
        # Print summary
        schema_info = inspect_database_schema()
        print(f"\n📊 Schema Summary:")
        print(f"   Tables: {len(schema_info['tables'])}")
        total_rows = sum(table['row_count'] for table in schema_info['tables'].values())
        print(f"   Total Rows: {total_rows}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating schema documentation: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
