-- Current Database Schema Export
-- Generated: 2025-07-18T08:27:04.598528

CREATE TABLE admin_users (
	name string NOT NULL,
	phone int,
	role string NOT NULL,
	email string NOT NULL,
	password string NOT NULL,
	id int NOT NULL,
	created_at DATETIME DEFAULT (now()),
	updated_at DATETIME DEFAULT (now()),
	status string NOT NULL,
	PRIMARY KEY (id),
	UNIQUE (email)
);


CREATE TABLE ai_training_feedback (
	id string NOT NULL,
	database_connection_id string NOT NULL,
	tenant_user_id int NOT NULL,
	query_history_id int,
	natural_language_query TEXT COLLATE "utf8mb4_unicode_ci" NOT NULL,
	generated_sql TEXT COLLATE "utf8mb4_unicode_ci",
	ai_response TEXT COLLATE "utf8mb4_unicode_ci",
	feedback_type string NOT NULL,
	feedback_score int,
	user_feedback TEXT COLLATE "utf8mb4_unicode_ci",
	corrected_sql TEXT COLLATE "utf8mb4_unicode_ci",
	expected_result JSON,
	query_intent string,
	tables_mentioned JSON,
	business_context TEXT COLLATE "utf8mb4_unicode_ci",
	learning_priority string NOT NULL,
	processed_for_training int NOT NULL,
	training_notes TEXT COLLATE "utf8mb4_unicode_ci",
	created_at DATETIME DEFAULT (now()),
	updated_at DATETIME DEFAULT (now()),
	PRIMARY KEY (id),
	FOREIGN KEY(database_connection_id) REFERENCES database_connections (id),
	FOREIGN KEY(tenant_user_id) REFERENCES tenant_users (id),
	FOREIGN KEY(query_history_id) REFERENCES query_history (id)
);

CREATE INDEX database_connection_id ON ai_training_feedback (database_connection_id);
CREATE INDEX query_history_id ON ai_training_feedback (query_history_id);
CREATE INDEX tenant_user_id ON ai_training_feedback (tenant_user_id);

CREATE TABLE api_keys (
	id int NOT NULL,
	tenant_id int NOT NULL,
	service_name string NOT NULL,
	key_name string NOT NULL,
	encrypted_key TEXT COLLATE "utf8mb4_unicode_ci" NOT NULL,
	status string NOT NULL,
	created_at DATETIME DEFAULT (now()),
	updated_at DATETIME DEFAULT (now()),
	last_used DATETIME,
	usage_count int,
	monthly_usage JSON,
	PRIMARY KEY (id),
	FOREIGN KEY(tenant_id) REFERENCES tenants (id)
);

CREATE INDEX tenant_id ON api_keys (tenant_id);

CREATE TABLE chats (
	id int NOT NULL,
	tenant_user_id int NOT NULL,
	database_connection_id string,
	title string NOT NULL,
	description TEXT COLLATE "utf8mb4_unicode_ci",
	created_at DATETIME DEFAULT (now()),
	updated_at DATETIME DEFAULT (now()),
	settings JSON,
	PRIMARY KEY (id),
	FOREIGN KEY(tenant_user_id) REFERENCES tenant_users (id),
	FOREIGN KEY(database_connection_id) REFERENCES database_connections (id)
);

CREATE INDEX database_connection_id ON chats (database_connection_id);
CREATE INDEX tenant_user_id ON chats (tenant_user_id);

CREATE TABLE database_columns (
	id string NOT NULL,
	table_id string NOT NULL,
	column_name string NOT NULL,
	data_type string NOT NULL,
	is_nullable int NOT NULL,
	is_primary_key int NOT NULL,
	is_foreign_key int NOT NULL,
	is_unique int NOT NULL,
	is_indexed int NOT NULL,
	default_value string,
	max_length int,
	business_name string,
	business_description TEXT COLLATE "utf8mb4_unicode_ci",
	data_category string,
	sample_values JSON,
	value_patterns JSON,
	data_quality_notes TEXT COLLATE "utf8mb4_unicode_ci",
	ai_description TEXT COLLATE "utf8mb4_unicode_ci",
	common_filters JSON,
	aggregation_patterns JSON,
	created_at DATETIME DEFAULT (now()),
	updated_at DATETIME DEFAULT (now()),
	PRIMARY KEY (id),
	FOREIGN KEY(table_id) REFERENCES database_tables (id)
);

CREATE INDEX table_id ON database_columns (table_id);

CREATE TABLE database_connections (
	id string NOT NULL,
	tenant_id int NOT NULL,
	name string NOT NULL,
	db_type string NOT NULL,
	host string,
	port int,
	database_name string NOT NULL,
	username string,
	password_encrypted TEXT COLLATE "utf8mb4_unicode_ci",
	connection_string TEXT COLLATE "utf8mb4_unicode_ci",
	status string NOT NULL,
	created_at DATETIME DEFAULT (now()),
	updated_at DATETIME DEFAULT (now()),
	last_tested DATETIME,
	metadata_info JSON,
	business_context JSON,
	ai_training_data JSON,
	schema_last_updated DATETIME,
	PRIMARY KEY (id),
	FOREIGN KEY(tenant_id) REFERENCES tenants (id)
);

CREATE INDEX tenant_id ON database_connections (tenant_id);

CREATE TABLE database_tables (
	id string NOT NULL,
	database_connection_id string NOT NULL,
	table_name string NOT NULL,
	table_type string,
	business_name string,
	business_description TEXT COLLATE "utf8mb4_unicode_ci",
	business_domain string,
	row_count int,
	estimated_size_mb FLOAT,
	last_analyzed DATETIME,
	common_queries JSON,
	query_patterns JSON,
	ai_description TEXT COLLATE "utf8mb4_unicode_ci",
	created_at DATETIME DEFAULT (now()),
	updated_at DATETIME DEFAULT (now()),
	PRIMARY KEY (id),
	FOREIGN KEY(database_connection_id) REFERENCES database_connections (id)
);

CREATE INDEX database_connection_id ON database_tables (database_connection_id);

CREATE TABLE messages (
	id int NOT NULL,
	chat_id int NOT NULL,
	tenant_user_id int,
	role string NOT NULL,
	content TEXT COLLATE "utf8mb4_unicode_ci" NOT NULL,
	created_at DATETIME DEFAULT (now()),
	updated_at DATETIME DEFAULT (now()),
	metadata_info JSON,
	PRIMARY KEY (id),
	FOREIGN KEY(chat_id) REFERENCES chats (id),
	FOREIGN KEY(tenant_user_id) REFERENCES tenant_users (id)
);

CREATE INDEX chat_id ON messages (chat_id);
CREATE INDEX tenant_user_id ON messages (tenant_user_id);

CREATE TABLE query_history (
	id int NOT NULL,
	tenant_user_id int NOT NULL,
	database_connection_id string,
	message_id int,
	chat_id int,
	natural_language_query TEXT COLLATE "utf8mb4_unicode_ci" NOT NULL,
	generated_sql TEXT COLLATE "utf8mb4_unicode_ci",
	query_result JSON,
	execution_status string NOT NULL,
	execution_time int,
	rows_affected int,
	error_message TEXT COLLATE "utf8mb4_unicode_ci",
	query_complexity string,
	tables_involved JSON,
	query_source string NOT NULL,
	created_at DATETIME DEFAULT (now()),
	updated_at DATETIME DEFAULT (now()),
	PRIMARY KEY (id),
	FOREIGN KEY(tenant_user_id) REFERENCES tenant_users (id),
	FOREIGN KEY(database_connection_id) REFERENCES database_connections (id),
	FOREIGN KEY(message_id) REFERENCES messages (id),
	FOREIGN KEY(chat_id) REFERENCES chats (id)
);

CREATE INDEX chat_id ON query_history (chat_id);
CREATE INDEX database_connection_id ON query_history (database_connection_id);
CREATE INDEX message_id ON query_history (message_id);
CREATE INDEX tenant_user_id ON query_history (tenant_user_id);

CREATE TABLE table_relationships (
	id string NOT NULL,
	source_table_id string NOT NULL,
	target_table_id string NOT NULL,
	relationship_type string NOT NULL,
	source_columns JSON NOT NULL,
	target_columns JSON NOT NULL,
	relationship_name string,
	business_description TEXT COLLATE "utf8mb4_unicode_ci",
	join_patterns JSON,
	ai_description TEXT COLLATE "utf8mb4_unicode_ci",
	created_at DATETIME DEFAULT (now()),
	updated_at DATETIME DEFAULT (now()),
	PRIMARY KEY (id),
	FOREIGN KEY(source_table_id) REFERENCES database_tables (id),
	FOREIGN KEY(target_table_id) REFERENCES database_tables (id)
);

CREATE INDEX source_table_id ON table_relationships (source_table_id);
CREATE INDEX target_table_id ON table_relationships (target_table_id);

CREATE TABLE tenant_ai_profiles (
	id string NOT NULL,
	tenant_id int NOT NULL,
	database_connection_id string NOT NULL,
	business_domain string,
	industry_vertical string,
	business_description TEXT COLLATE "utf8mb4_unicode_ci",
	preferred_query_style string,
	response_tone string,
	explanation_level string,
	business_glossary JSON,
	common_abbreviations JSON,
	preferred_terminology JSON,
	successful_query_patterns JSON,
	problematic_areas JSON,
	improvement_priorities JSON,
	query_success_rate FLOAT,
	user_satisfaction_score FLOAT,
	last_performance_review DATETIME,
	created_at DATETIME DEFAULT (now()),
	updated_at DATETIME DEFAULT (now()),
	PRIMARY KEY (id),
	FOREIGN KEY(tenant_id) REFERENCES tenants (id),
	FOREIGN KEY(database_connection_id) REFERENCES database_connections (id)
);

CREATE INDEX database_connection_id ON tenant_ai_profiles (database_connection_id);
CREATE INDEX tenant_id ON tenant_ai_profiles (tenant_id);

CREATE TABLE tenant_users (
	name string NOT NULL,
	role string NOT NULL,
	phone int,
	tenant_id int NOT NULL,
	hashed_password string NOT NULL,
	is_superuser int,
	last_login DATETIME,
	id int NOT NULL,
	created_at DATETIME DEFAULT (now()),
	updated_at DATETIME DEFAULT (now()),
	status string NOT NULL,
	email string NOT NULL,
	PRIMARY KEY (id),
	FOREIGN KEY(tenant_id) REFERENCES tenants (id)
);

CREATE INDEX ix_tenant_users_email ON tenant_users (email);
CREATE INDEX tenant_id ON tenant_users (tenant_id);

CREATE TABLE tenants (
	employer_name string NOT NULL,
	size int,
	phone int,
	id int NOT NULL,
	created_at DATETIME DEFAULT (now()),
	updated_at DATETIME DEFAULT (now()),
	status string NOT NULL,
	email string NOT NULL,
	PRIMARY KEY (id)
);

CREATE INDEX ix_tenants_email ON tenants (email);
CREATE INDEX ix_tenants_employer_name ON tenants (employer_name);

CREATE TABLE users (
	id string NOT NULL,
	email string NOT NULL,
	username string,
	full_name string,
	hashed_password string NOT NULL,
	is_active int,
	is_superuser int,
	created_at DATETIME DEFAULT (now()),
	updated_at DATETIME DEFAULT (now()),
	last_login DATETIME,
	PRIMARY KEY (id),
	UNIQUE (email),
	UNIQUE (username)
);

