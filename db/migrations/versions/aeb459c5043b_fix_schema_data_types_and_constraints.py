"""Fix schema data types and constraints

Revision ID: aeb459c5043b
Revises: 7e17eee05bfd
Create Date: 2025-07-18 07:54:28.010695

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'aeb459c5043b'
down_revision: Union[str, Sequence[str], None] = '7e17eee05bfd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to match desired specifications."""

    # Add password length constraint (minimum 8 characters) for admin_users
    try:
        op.execute("ALTER TABLE admin_users ADD CONSTRAINT chk_admin_password_length CHECK (CHAR_LENGTH(password) >= 8)")
    except Exception:
        # Constraint might already exist, ignore
        pass

    # Add password length constraint for tenant_users
    try:
        op.execute("ALTER TABLE tenant_users ADD CONSTRAINT chk_tenant_password_length CHECK (CHAR_LENGTH(hashed_password) >= 8)")
    except Exception:
        # Constraint might already exist, ignore
        pass

    # Add comment about settings JSON in chats table
    try:
        op.execute("ALTER TABLE chats MODIFY COLUMN settings JSON COMMENT 'Stores chat-specific settings like preferred response style, complexity level, auto-suggestions, etc.'")
    except Exception:
        # Comment might already exist, ignore
        pass


def downgrade() -> None:
    """Downgrade schema changes."""

    # Remove password constraints
    try:
        op.execute("ALTER TABLE admin_users DROP CONSTRAINT chk_admin_password_length")
    except Exception:
        pass

    try:
        op.execute("ALTER TABLE tenant_users DROP CONSTRAINT chk_tenant_password_length")
    except Exception:
        pass

    # Remove comment
    try:
        op.execute("ALTER TABLE chats MODIFY COLUMN settings JSON")
    except Exception:
        pass
