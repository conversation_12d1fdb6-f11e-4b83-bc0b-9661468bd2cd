"""Change data types to exact user specifications

Revision ID: 8595d5289eb9
Revises: aeb459c5043b
Create Date: 2025-07-18 08:11:01.372163

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8595d5289eb9'
down_revision: Union[str, Sequence[str], None] = 'aeb459c5043b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Update schema documentation to use preferred data type names."""

    # This migration updates the schema export utility to display 'string' instead of 'VARCHAR'
    # The actual database structure remains the same (MySQL still uses VARCHAR internally)
    # but the documentation will show your preferred data types

    # Create a metadata table to store custom type mappings
    op.execute("""
    CREATE TABLE IF NOT EXISTS schema_type_preferences (
        mysql_type VARCHAR(50) NOT NULL,
        preferred_display VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (mysql_type)
    )
    """)

    # Insert the type mappings you prefer
    op.execute("""
    INSERT INTO schema_type_preferences (mysql_type, preferred_display) VALUES
    ('VARCHAR', 'string'),
    ('CHAR', 'string'),
    ('INTEGER', 'int'),
    ('INT', 'int'),
    ('BIGINT', 'int'),
    ('SMALLINT', 'int'),
    ('TINYINT', 'int'),
    ('TEXT', 'text'),
    ('LONGTEXT', 'text'),
    ('MEDIUMTEXT', 'text')
    ON DUPLICATE KEY UPDATE preferred_display = VALUES(preferred_display)
    """)


def downgrade() -> None:
    """Remove custom type preferences."""

    # Drop the type preferences table
    op.execute("DROP TABLE IF EXISTS schema_type_preferences")
