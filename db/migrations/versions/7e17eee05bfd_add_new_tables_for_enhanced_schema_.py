"""Add new tables for enhanced schema storage

Revision ID: 7e17eee05bfd
Revises: 955ad0d164a2
Create Date: 2025-07-18 06:10:56.108875

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7e17eee05bfd'
down_revision: Union[str, Sequence[str], None] = '955ad0d164a2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
