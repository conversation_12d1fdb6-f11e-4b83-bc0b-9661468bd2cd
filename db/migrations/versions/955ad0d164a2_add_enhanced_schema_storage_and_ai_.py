"""Add enhanced schema storage and AI training models

Revision ID: 955ad0d164a2
Revises: 34cbd575694b
Create Date: 2025-07-18 06:10:14.290443

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '955ad0d164a2'
down_revision: Union[str, Sequence[str], None] = '34cbd575694b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('database_connections', sa.Column('business_context', sa.JSON(), nullable=True))
    op.add_column('database_connections', sa.Column('ai_training_data', sa.JSON(), nullable=True))
    op.add_column('database_connections', sa.Column('schema_last_updated', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('database_connections', 'schema_last_updated')
    op.drop_column('database_connections', 'ai_training_data')
    op.drop_column('database_connections', 'business_context')
    # ### end Alembic commands ###
